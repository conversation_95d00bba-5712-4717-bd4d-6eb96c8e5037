<template>
  <div class="roow-div">
    <div ref="mapRef" id="player" class="map-container"></div>
    <div class="screen-wrap">
      <!-- 导航栏 -->
      <div class="nav-bar">
        <div
          v-for="(item, index) in navItems"
          :key="index"
          class="nav-item"
          :class="{ active: activeNavButton === index }"
          @click="changeNav(index)"
        >
          {{ item.name }}
        </div>
      </div>

      <!-- 中间顶部背景图 -->
      <div class="middle-top">
        <div class="section-title">内涝安全预警监测综合驾驶舱系统</div>
      </div>
      <!-- 中间悬浮列表 -->
      <div class="middle-tabs">
        <div
          v-for="(item, index) in middleTabItems"
          :key="index"
          class="tab-item"
          :class="{ active: activeTabIndex === index }"
          @click="changeTabs(item.name, index)"
        >
          <div class="tab-text">{{ item.name }}</div>
        </div>
      </div>

      <!-- 右侧列表面板 -->
      <div v-if="showRightPanel" class="right-panel panel-visible">
        <div class="panel-header">
          <div class="panel-title">{{ currentTabName }}</div>
          <div class="panel-close" @click="closeRightPanel">×</div>
        </div>
        <div class="panel-content">
          <div class="list-container">
            <!-- 重点场景列表 -->
            <template v-if="currentTabName === '重点场景'">
              <div
                v-for="(item, index) in zdscType"
                :key="index"
                class="list-item scene-item"
                :class="{ selected: item.selected }"
                @click="handleListItemClick(item)"
              >
                <div class="item-content">
                  <div class="item-checkbox">
                    <span class="checkbox-icon" :class="{ checked: item.selected }">
                      {{ item.selected ? '✓' : '' }}
                    </span>
                  </div>
                  <div class="item-title">{{ item.sstype }}</div>
                </div>
              </div>
            </template>

            <!-- 异常预警列表 -->
            <template v-if="currentTabName === '异常预警'">
              <div
                v-for="(item, index) in warningType"
                :key="index"
                class="list-item scene-item"
                :class="{ selected: item.selected }"
                @click="handleWarningItemClick(item)"
              >
                <div class="item-content">
                  <div class="item-checkbox">
                    <span class="checkbox-icon" :class="{ checked: item.selected }">
                      {{ item.selected ? '✓' : '' }}
                    </span>
                  </div>
                  <div class="item-title">{{ item.name }}</div>
                </div>
              </div>
            </template>

            <!-- 人员定位选择列表 -->
            <template v-if="currentTabName === '人员定位'">
              <div
                v-for="(item, index) in personnelType"
                :key="index"
                class="list-item scene-item"
                :class="{ selected: item.selected }"
                @click="handlePersonnelItemClick(item)"
              >
                <div class="item-content">
                  <div class="item-checkbox">
                    <span class="checkbox-icon" :class="{ checked: item.selected }">
                      {{ item.selected ? '✓' : '' }}
                    </span>
                  </div>
                  <div class="item-title">{{ item.name }}</div>
                  <div class="item-count">({{ item.count }})</div>
                </div>
              </div>
            </template>

            <!-- 防汛泵车选择列表 -->
            <template v-if="currentTabName === '防汛泵车'">
              <div
                v-for="(item, index) in pumpType"
                :key="index"
                class="list-item scene-item"
                :class="{ selected: item.selected }"
                @click="handlePumpItemClick(item)"
              >
                <div class="item-content">
                  <div class="item-checkbox">
                    <span class="checkbox-icon" :class="{ checked: item.selected }">
                      {{ item.selected ? '✓' : '' }}
                    </span>
                  </div>
                  <div class="item-title">{{ item.name }}</div>
                  <div class="item-count">({{ item.count }})</div>
                </div>
              </div>
            </template>

            <!-- 其他视频列表 -->
            <template v-if="currentTabName === '其他视频'">
              <!-- 视频搜索和筛选 -->
              <div class="video-filter">
                <!-- 搜索框 -->
                <div class="search-container">
                  <input
                    v-model="videoSearchText"
                    type="text"
                    placeholder="搜索"
                    class="search-input"
                    @input="handleVideoSearch"
                    @keyup.enter="handleVideoSearchClick"
                  />
                  <button class="search-button" @click="handleVideoSearchClick">查询</button>
                </div>

                <!-- 下拉筛选框 -->
                <div class="select-container">
                  <div class="select-label">视频类别:</div>
                  <select v-model="selectedVideoCategory" class="category-select" @change="handleCategoryChange">
                    <option value="全部">全部</option>
                    <option v-for="(type, index) in videoType" :key="index" :value="type.yycjmc">
                      {{ type.yycjmc }}
                    </option>
                  </select>
                </div>
              </div>

              <!-- 视频列表 -->
              <div class="video-list">
                <div
                  v-for="video in otherVideoData"
                  :key="video.id"
                  class="video-item"
                  @click="handleVideoItemClick(video)"
                >
                  <div class="video-info">
                    <div class="video-name">{{ video.sxtmc }}</div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>

      <div class="middle-content" v-if="activeNavButton === 1">
        <div class="top-tools">
          <el-button
            style="width: 150px; height: 100px; font-size: 30px; border-radius: 8px"
            type="primary"
            size="default"
            @click="hdlStart"
            >播放推演
          </el-button>
        </div>
        <div class="progress-wrapper">
          <el-slider
            class="floating-progress"
            :show-tooltip="false"
            v-model="progressValue"
            :step="1"
            :min="0"
            :max="10"
            :marks="twoDWaterSliderMarks"
            @change="handleProgressChange"
          />

          <!-- <el-button
            style="width: 150px; height: 100px; font-size: 30px; border-radius: 8px"
            type="primary"
            size="default"
            @click="hdlStart"
            >播放推演
          </el-button> -->
        </div>
        <!-- <div class="progress-wrapper">
          <el-slider
            class="floating-progress"
            show-tooltip="false"
            v-model="progressValue"
            :step="1"
            :min="0"
            :max="10"
            :marks="twoDWaterSliderMarks"
            @change="handleProgressChange"
          />
        </div> -->
      </div>

      <!-- 中间底部背景图 -->
      <div class="middle-bottom"></div>

      <!-- 这个里面放引用过来的组件 -->
      <div>
        <home
          v-if="activeNavButton === 0"
          @tagClicked="handleTagData"
          @clickTable="handleClickTable"
          @videoClicked="handleVideoClicked"
          @yuntuClickShow="yuntuClickShowP"
          @tagClickedbranch="handleTagDataBranch"
          @clickGoods="handleGoods"
          @clickScene="handleScene"
          @clickBc="handleBc"
          @clickWz="handleWz"
        />
        <intelligence
          v-if="activeNavButton === 2"
          @clickTable="handleClickTable"
          @videoClicked="handleVideoClicked"
          @tagClicked="handleTagData"
          @yuntuClickShow="yuntuClickShowP"
          @clickScene="handleScene"
          @tagClickedbranch="handleTagDataBranch"
          @ddCommand="handleDdCommand"
        />
        <callPolice
          v-if="activeNavButton === 1"
          @tagClicked="handleTagData"
          @clickTable="handleClickTable"
          @clickScene="handleScene"
          @yuntuClickShow="yuntuClickShowP"
          :shikeValue="currentShikeValue"
        />
        <assistantdecision
          v-if="activeNavButton === 3"
          @tagClicked="handleTagData"
          @clickTable="handleClickTable"
          @yuntuClickShow="yuntuClickShowP"
          @clickDate="handleDate"
        />
      </div>

      <map-popup
        ref="mapPopupRef"
        v-model:visible="dialogVisible"
        title=""
        :show-close="true"
        :close-on-click-modal="true"
        :destroy-on-close="true"
        class="map-popup-container"
        :device-data="deviceData"
        :single-marker-data="singleMarkerData"
        @close="handleClose"
      >
        <!-- 弹窗内容已移至组件内部 -->
      </map-popup>
      <!-- 云图iframe弹窗 -->
      <div
        v-if="dialogVisibleYuntu && middleImgShow === 1"
        class="yuntu-iframe-dialog-overlay"
        @click="handleOverlayClick"
        @mousedown="handleOverlayClick"
      >
        <div class="yuntu-iframe-dialog" @click.stop @mousedown.stop>
          <!-- 标题栏 -->
          <div class="dialog-header">
            <div class="dialog-title">云图</div>
            <div class="dialog-close" @click="closeYuntuDialog">×</div>
          </div>

          <!-- 内容区域 -->
          <div class="dialog-content">
            <iframe
              src="https://www.aifcst.com/home"
              frameborder="0"
              width="100%"
              height="1000px"
              style="border: none; border-radius: 8px"
              @load="handleIframeLoad"
              @error="handleIframeError"
            ></iframe>
          </div>
        </div>
      </div>

      <!-- 物资table弹窗 -->
      <el-dialog modal-class="supplies-dialog" v-model="yjwzdialogVisible" title="应急物资" width="1500">
        <div style="margin-top: 40px">
          <el-table
            :data="yjwztableData"
            height="700"
            style="width: 100%; margin-bottom: 32px; background: transparent"
          >
            <el-table-column prop="wzlx" label="名称" />
            <el-table-column prop="ckname" label="仓库" />
            <el-table-column prop="jldw" label="单位" />
            <el-table-column prop="sl" label="数量" />
          </el-table>

          <el-pagination
            v-model:current-page="yjwzPageNum"
            v-model:page-size="yjwzPageSize"
            :page-sizes="[20, 40, 80, 100, 200]"
            size="large"
            layout="total, prev, pager, next, jumper"
            :total="yjwzTotal"
            @size-change="handleSizeChangeyjwz"
            @current-change="handleCurrentChangeyjwz"
          />
        </div>
      </el-dialog>

      <!-- 自定义弹窗 -->
      <div v-if="deviceDialogVisible" class="device-dialog-overlay">
        <div class="device-dialog" @click.stop>
          <!-- 弹窗头部 -->
          <div class="device-dialog-header">
            <div class="device-dialog-title">{{ deviceDetails?.sbname }}</div>
            <div class="device-dialog-close" @click="handledeviceClose">×</div>
          </div>

          <!-- 弹窗内容 -->
          <div class="device-dialog-body">
            <div class="device-dialog-content">
              <!-- 监测数据区域 -->
              <div class="monitoring-section">
                <div class="monitoring-title">监测数据</div>

                <!-- 图表区域 -->
                <div class="charts-container">
                  <div class="chart-item">
                    <div class="chart-wrapper" ref="chart1Ref"></div>
                  </div>
                  <div class="chart-item">
                    <div class="chart-wrapper" ref="chart2Ref"></div>
                  </div>
                </div>

                <!-- 数据表格区域 -->
                <div class="data-table-section">
                  <el-tabs v-model="tabsCctiveName" class="tas">
                    <el-tab-pane label="权责信息" name="qzxx">
                      <div class="table-container">
                        <table class="info-table">
                          <thead>
                            <tr>
                              <!-- <th>泵站</th> -->
                              <th>权责部门</th>
                              <th>权责类型</th>
                              <th>联系方式</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr v-for="(item, index) in qzxxData" :key="index">
                              <!-- <td>
                                <div class="pump-station-cell">
                                  <img
                                    :src="bengzhanImage"
                                    alt="泵站"
                                    class="pump-station-thumbnail"
                                    @click="openImagePreview(bengzhanImage, '泵站图片')"
                                  />
                                </div>
                              </td> -->

                              <td>{{ item.qzbmqy || '-' }}</td>
                              <td>{{ item.qzlx || '-' }}</td>
                              <td style="cursor: pointer" @click="handlePhoneCall(item.lxfs)">
                                {{ item.lxfs || '-' }}
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane label="视频监控" name="spjk">
                      <div class="video-section">
                        <div class="video-content">
                          <!-- 视频信息列表 -->
                          <div class="video-info-list">
                            <div
                              v-for="(item, index) in videoData"
                              :key="index"
                              class="video-info"
                              :class="{ active: selectedVideoIndex === index }"
                              @click="selectVideo(index)"
                            >
                              {{ item.name || '暂无视频名称' }}
                            </div>
                          </div>
                          <!-- 视频播放器 -->
                          <div class="video-player" v-if="selectedVideo">
                            <img
                              :src="`https://picsum.photos/397/199?random=${Math.floor(Math.random() * 1000)}`"
                              alt="视频占位图"
                              style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px"
                            />
                          </div>
                          <!-- 无视频时的占位 -->
                          <div class="video-player-placeholder" v-else>
                            <div class="placeholder-text">请选择视频</div>
                          </div>
                        </div>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane label="管网信息" name="ssfq">
                      <div style="font-size: 24px">管网信息：管网信息</div>
                    </el-tab-pane>
                    <el-tab-pane label="汇水分区" name="hsfq">
                      <div style="font-size: 24px">汇水分区：{{ hsfqData?.sspsfq }}</div>
                    </el-tab-pane>
                    <el-tab-pane label="泵站组态" name="bz">
                      <img :src="bengzhanImage" class="preview-image" />
                    </el-tab-pane>
                  </el-tabs>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图片预览弹窗 -->
      <div v-if="imagePreviewVisible" class="image-preview-overlay" @click="closeImagePreview">
        <div class="image-preview-dialog" @click.stop>
          <!-- 弹窗头部 -->
          <div class="image-preview-header">
            <div class="image-preview-title">{{ imagePreviewTitle }}</div>
            <div class="image-preview-close" @click="closeImagePreview">×</div>
          </div>

          <!-- 图片内容 -->
          <div class="image-preview-content">
            <img :src="imagePreviewSrc" :alt="imagePreviewTitle" class="preview-image" />
          </div>
        </div>
      </div>
      <!-- 应急物资弹窗 -->
      <div v-if="ckPreviewVisible" class="car-preview-overlay" @click="closeCkPreview">
        <!-- 弹窗头部 -->
        <div class="car-dialog-header">
          <div class="car-dialog-title">应急物资信息</div>
          <div class="car-dialog-close" @click="closeCkPreview">×</div>
        </div>
        <div class="car-preview-dialog">
          <div class="car-info">仓库：{{ ckData?.name }}</div>
          <div class="car-info">所在区域：{{ ckData?.address }}</div>
          <div class="car-info">责任单位：{{ ckData?.zrdw || '暂无' }}</div>
          <div class="car-info">负责人：{{ ckData?.fzr }}</div>
        </div>
      </div>

      <!-- 视频弹窗 -->
      <div v-if="vdPreviewVisible" class="car-preview-overlay" @click="closeVdPreview">
        <!-- 弹窗头部 -->
        <div class="car-dialog-header">
          <div class="car-dialog-title">视频信息</div>
          <div class="car-dialog-close" @click="closeVdPreview">×</div>
        </div>
        <div class="car-preview-dialog">
          <div class="car-info">摄像头名称：{{ vdDetails?.sxtmc }}</div>
          <div class="car-info">应用场景：{{ vdDetails?.yycjmc }}</div>
          <div class="car-info">时间：{{ vdDetails?.cjsj }}</div>
          <div class="car-vd">
            <!-- 视频播放区域 -->
            <div v-if="vdDetails?.urlData" class="video-player-container">
              <video id="car-video-player" controls playsinline class="video-player"></video>
            </div>
            <!-- 无视频时显示 -->
            <div v-else class="no-video-container">
              <div class="no-video-icon">📹</div>
              <div class="no-video-text">暂无视频</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 防汛人员弹窗 -->
      <div v-if="peoplePreviewVisible" class="car-preview-overlay" @click="closePeoplePreview">
        <!-- 弹窗头部 -->
        <div class="car-dialog-header">
          <div class="car-dialog-title">防汛人员信息</div>
          <div class="car-dialog-close" @click="closePeoplePreview">×</div>
        </div>
        <div class="car-preview-dialog">
          <div class="car-info">队伍名称：{{ fxryData?.teamDuty }}</div>
          <div class="car-info">所在区域：{{ fxryData?.locationArea }}</div>
          <div class="car-info">储备点地址：{{ fxryData?.reservePointAddress }}</div>
          <div class="car-info">负责人：{{ fxryData?.leader }}</div>
          <div class="car-info">队伍职责：{{ fxryData?.teamDuty }}</div>
        </div>
      </div>

      <!-- 防汛车辆弹窗 -->
      <div v-if="carPreviewVisible" class="car-preview-overlay" @click="closeCarPreview">
        <!-- 弹窗头部 -->
        <div class="car-dialog-header">
          <div class="car-dialog-title">防汛车辆信息</div>
          <div class="car-dialog-close" @click="closeCarPreview">×</div>
        </div>
        <div class="car-preview-dialog">
          <div class="car-info">车牌号码：{{ deviceData?.cphm }}</div>
          <div class="car-info">车辆类型：{{ deviceData?.cllx }}</div>
          <div class="car-info">驾驶⼈姓名：{{ deviceData?.jsrxm }}</div>
          <div class="car-info">驾驶⼈联系电话：{{ deviceData?.jsrlxdh }}</div>
          <div class="car-info">所属部⻔：{{ deviceData?.ssbm }}</div>
          <div class="car-info">责任⼈姓名：{{ deviceData?.zrrxm }}</div>
        </div>
      </div>
      <!-- 调度指令统计弹窗 -->
      <div v-if="diaoduzhilingtongjiVisible" class="car-preview-overlay" @click="diaoduzhilingtongjiVisible = false">
        <!-- 弹窗头部 -->
        <div class="car-dialog-header">
          <div class="car-dialog-title">调度指令统计</div>
          <div class="car-dialog-close" @click="diaoduzhilingtongjiVisible = false">×</div>
        </div>
        <div class="car-preview-dialog">
          <div class="car-info">指令类别：{{ diaoduzhilingObject?.command_type }}</div>
          <div class="car-info">调度类别：{{ diaoduzhilingObject?.adjustment_type }}</div>
          <div class="car-info">指令发布时间：{{ diaoduzhilingObject?.command_release_time }}</div>
          <div class="car-info">调度内容：{{ diaoduzhilingObject?.adjustment_content }}</div>
          <div class="car-info">调度次数：{{ diaoduzhilingObject?.adjustment_times }}</div>
        </div>
      </div>
      <!-- 应急泵车弹窗 -->
      <div v-if="yjbcPreviewVisible" class="car-preview-overlay" @click="closeyjbcPreview">
        <!-- 弹窗头部 -->
        <div class="car-dialog-header">
          <div class="car-dialog-title">应急泵车</div>
          <div class="car-dialog-close" @click="closeyjbcPreview">×</div>
        </div>
        <div class="car-preview-dialog">
          <div class="car-info">车牌号码：{{ deviceData?.cphm }}</div>
          <div class="car-info">车辆类型：{{ deviceData?.cllx }}</div>
          <div class="car-info">驾驶⼈姓名：{{ deviceData?.jsrxm }}</div>
          <div class="car-info">驾驶⼈联系电话：{{ deviceData?.jsrlxdh }}</div>
          <div class="car-info">所属部⻔：{{ deviceData?.ssbm }}</div>
          <div class="car-info">责任⼈姓名：{{ deviceData?.zrrxm }}</div>
        </div>
      </div>

      <!-- 单点表格弹窗 -->
      <div v-if="tablePreviewVisible" class="table-preview-overlay" @click="closeTablePreview">
        <!-- 弹窗头部 -->
        <!-- 预报积水点统计 -->
        <div class="car-dialog-header">
          <div class="car-dialog-title" v-if="singleMarkerData.tableData.name">
            {{ singleMarkerData.tableData.name }}
          </div>
          <!-- 设备报警情况 -->
          <div v-if="singleMarkerData.tableData.device_name" class="car-dialog-title">
            <span class="popup-value">{{ singleMarkerData.tableData.device_name }}</span>
          </div>
          <!-- 积涝统计 -->
          <div v-if="singleMarkerData.tableData?.station" class="car-dialog-title">
            <span class="popup-value">{{ singleMarkerData.tableData?.station }}</span>
          </div>
          <div v-if="singleMarkerData.tableData.bcname" class="car-dialog-title">
            名称： <span class="popup-value">{{ singleMarkerData.tableData.bcname }}</span>
          </div>
          <div v-if="singleMarkerData.tableData.wzname" class="car-dialog-title">
            名称： <span class="popup-value">{{ singleMarkerData.tableData.wzname }}</span>
          </div>
          <div class="car-dialog-close" @click="closeCarPreview">×</div>
        </div>
        <div class="car-preview-dialog">
          <div class="car-info" v-if="singleMarkerData.tableData.type">
            类型：<span class="popup-value">{{ singleMarkerData.tableData.type }}</span>
          </div>
          <div class="car-info" v-if="singleMarkerData.tableData.alarmLevel">
            报警等级：<span class="popup-value">{{ singleMarkerData.tableData.alarmLevel }}</span>
          </div>
          <div class="car-info" v-if="singleMarkerData.tableData.grade">
            预报等级：<span class="popup-value">{{ singleMarkerData.tableData.grade }}</span>
          </div>
          <div class="car-info" v-if="singleMarkerData.tableData.waterDepth">
            水位：<span class="popup-value">{{ singleMarkerData.tableData.waterDepth }}</span>
          </div>
          <div class="car-info" v-if="singleMarkerData.tableData.forecastTime">
            报警时间：<span class="popup-value">{{ singleMarkerData.tableData.forecastTime }}</span>
          </div>
          <div class="car-info" v-if="singleMarkerData.tableData.name">
            地址：<span class="popup-value">{{ singleMarkerData.tableData.name }}</span>
          </div>
          <div class="car-info" v-if="singleMarkerData.tableData.waterTime">
            持续时间：<span class="popup-value">{{ singleMarkerData.tableData.waterTime }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.accumulated_area" class="car-info">
            积水面积：<span class="popup-value">{{ singleMarkerData.tableData?.accumulated_area }} ㎡</span>
          </div>
          <div v-if="singleMarkerData.tableData?.max_water_depth" class="car-info">
            最大水深：<span class="popup-value">{{ singleMarkerData.tableData?.max_water_depth }} m</span>
          </div>
          <div v-if="singleMarkerData.tableData?.alert_level" class="car-info">
            预警等级：<span class="popup-value">{{ singleMarkerData.tableData?.alert_level }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.alert_time" class="car-info">
            预警时间：<span class="popup-value">{{ singleMarkerData.tableData?.alert_time }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.eventGridName" class="car-info">
            事件位置：<span class="popup-value">{{ singleMarkerData.tableData?.eventGridName }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.eventName" class="car-info">
            上报人：<span class="popup-value">{{ singleMarkerData.tableData?.eventName }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.eventRepDepartName" class="car-info">
            事件处置部门<span class="popup-value">{{ singleMarkerData.tableData?.eventRepDepartName }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.eventStatusName" class="car-info">
            审核状态<span class="popup-value">{{ singleMarkerData.tableData?.eventStatusName }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.eventTypeName" class="car-info">
            事件描述：<span class="popup-value">{{ singleMarkerData.tableData?.eventTypeName }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.rptTime" class="car-info">
            事件发生事件：<span class="popup-value">{{ singleMarkerData.tableData?.rptTime }}</span>
          </div>

          <div v-if="singleMarkerData.tableData?.time_period" class="car-info">
            时段：<span class="popup-value">{{ singleMarkerData.tableData?.time_period }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.water_change" class="car-info">
            积水面积：<span class="popup-value">{{ singleMarkerData.tableData?.water_change }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.water_storage_time" class="car-info">
            积水深度：<span class="popup-value">{{ singleMarkerData.tableData?.water_storage_time }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.area_expansion" class="car-info">
            积水面积：<span class="popup-value">{{ singleMarkerData.tableData?.area_expansion }}</span>
          </div>

          <div v-if="singleMarkerData.tableData?.status" class="car-info">
            泵站状况：<span class="popup-value">{{ singleMarkerData.tableData?.status }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.pumping_volume" class="car-info">
            抽水量：<span class="popup-value">{{ singleMarkerData.tableData?.pumping_volume }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.discharge_volume" class="car-info">
            抽排量：<span class="popup-value">{{ singleMarkerData.tableData?.discharge_volume }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.shutdown_status" class="car-info">
            启停状况：<span class="popup-value">{{ singleMarkerData.tableData?.shutdown_status }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.location" class="car-info">
            地址：<span class="popup-value">{{ singleMarkerData.tableData?.location }}</span>
          </div>
          <div v-if="singleMarkerData.tableData.device_name" class="car-info">
            排水分区： <span class="popup-value">{{ singleMarkerData.tableData.device_name }}</span>
          </div>

          <div v-if="singleMarkerData.tableData.quantity" class="car-info">
            数量： <span class="popup-value">{{ singleMarkerData.tableData.quantity }}</span>
          </div>
          <div v-if="singleMarkerData.tableData.responsible_person" class="car-info">
            负责人： <span class="popup-value">{{ singleMarkerData.tableData.responsible_person }}</span>
          </div>
          <div v-if="singleMarkerData.tableData.response_time" class="car-info">
            响应时间： <span class="popup-value">{{ singleMarkerData.tableData.response_time }}</span>
          </div>
        </div>
      </div>

      <div v-if="callphonePreviewVisible" class="call-preview-overlay" @click="closecallphonePreview">
        <div class="car-dialog-header">
          <div class="car-dialog-close" @click="closeCarPreview">×</div>
        </div>
        <div class="content">
          <callPhone :phoneNumber="phoneNumber" />
        </div>
      </div>
    </div>

    <!-- 原有的云中快报弹窗 -->
    <el-dialog
      modal-class="yuntu"
      v-model="dialogVisibleYuntu"
      width="2180px"
      :close-on-click-modal="true"
      :show-close="true"
      :destroy-on-close="true"
      class="yuntu-dialog"
      v-if="middleImgShow === 2"
    >
      <div style="height: 20px"></div>
      <div class="yunzhongkuaibao-img"></div>
    </el-dialog>

    <!-- 防汛简报弹窗 -->
    <el-dialog
      v-model="floodReportVisible"
      title="防汛简报"
      width="1500px"
      :close-on-click-modal="true"
      :show-close="true"
      :destroy-on-close="true"
      class="flood-report-dialog"
    >
      <div class="flood-report-container">
        <!-- 表格 -->
        <div class="flood-table-wrapper" style="height: 500px; overflow-y: auto;">
          <table class="flood-native-table">
            <thead>
              <tr>
                <th >简报名称</th>
                <th >汛情时间</th>
                <th >报告时间</th>
                <th >操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="floodReportData.length === 0">
                <td colspan="5" style="text-align: center; padding: 40px; color: #94a3b8;">
                  暂无数据
                </td>
              </tr>
              <tr v-for="(row, index) in floodReportData" :key="row.id || index" v-else>
                <td>{{ row.title }}</td>
                <td>{{ row.floodSeasonTime }}</td>
                <td>{{ row.reportTime }}</td>
                <td>
                  <el-button
                    type="primary"
                    size="small"
                    @click="handlePreview(row)"
                    class="flood-action-btn preview-btn"
                  >
                    预览
                  </el-button>
                  <el-button
                    type="success"
                    size="small"
                    @click="handleDownload(row)"
                    class="flood-action-btn download-btn"
                    style="margin-left: 8px;"
                  >
                    下载
                  </el-button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div class="flood-pagination-container">
          <div class="pagination-controls">
            <el-pagination
              v-model:current-page="floodReportPageNum"
              v-model:page-size="floodReportPageSize"
              :page-sizes="[10, 20, 50]"
              size="small"
              layout="sizes, prev, pager, next, jumper"
              :total="floodReportTotal"
              @size-change="handleFloodReportSizeChange"
              @current-change="handleFloodReportPageChange"
              class="flood-report-pagination"
            />
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script setup>
import { onMounted, ref, onUnmounted, computed, nextTick } from 'vue'
import * as acapi from '../../../public/js/ac.min.js'
import Hls from 'hls.js'
import assistantdecision from './assistantdecision.vue'
import home from './home.vue'
import { useRouter } from 'vue-router'
import callPolice from './callPolice1.vue'
import intelligence from './intelligence2.vue'
import { yingjiwuziRootTable } from '@/api/intelligence'
import * as echarts from 'echarts'
import MapPopup from '@/components/mapPopup.vue'
import bengzhanImage from '@/assets/images/bengzhan.png'
import callPhone from '@/components/callPhone.vue'
import {
  getDeviceDetails1,
  getSuppliesDetails,
  getCarData,
  getdeviceInfo,
  getwaterLevel,
  getQzxx,
  getVideoInfo,
  getWzData,
  getyldfq,
  getZdcjType,
  getZdcj,
  getRydw,
  getYcyj,
  getRydwMap,
  getFxryData,
  getFxbc,
  getFxbcMap,
  getCkMap,
  getCkDataList,
  getVideo,
  getVideoType,
  getVideoDeatils,
  getFloodReport
} from '@/api/home.js'

const vdDetails = ref(null)
const getvideoDeatilsData = async id => {
  const result = await getVideoDeatils(id)
  if (result.code === 200) {
    vdDetails.value = result.data
    console.log('获取视频详情成功:', vdDetails.value)

    // 获取视频详情后，初始化视频播放
    nextTick(() => {
      initVideoPlayer()
    })
  }
}

// 初始化视频播放器
const initVideoPlayer = () => {
  try {
    console.log('开始初始化视频播放器')
    console.log('视频详情数据:', vdDetails.value)

    // 获取视频元素
    const videoElement = document.getElementById('car-video-player')
    if (!videoElement) {
      console.error('无法找到视频元素 #car-video-player')
      return
    }

    // 检查是否有urlData
    if (!vdDetails.value?.urlData) {
      console.log('没有视频数据，显示暂无视频')
      return
    }

    // 解析urlData字符串
    let videoUrl = ''
    try {
      console.log('原始urlData:', vdDetails.value.urlData)

      // 先解析外层JSON
      const urlDataObj = JSON.parse(vdDetails.value.urlData)
      console.log('解析后的urlDataObj:', urlDataObj)

      // 再解析内层url字符串
      if (urlDataObj && urlDataObj.url) {
        const urlObj = JSON.parse(urlDataObj.url)
        console.log('解析后的urlObj:', urlObj)

        // 获取hls格式的视频URL
        videoUrl = urlObj.hls
        console.log('提取的视频URL:', videoUrl)
      }
    } catch (error) {
      console.error('解析视频URL数据失败:', error)
      return
    }

    if (videoUrl) {
      console.log('开始加载视频:', videoUrl)

      // 检查浏览器是否支持HLS
      if (Hls.isSupported()) {
        const hls = new Hls()
        hls.loadSource(videoUrl)
        hls.attachMedia(videoElement)

        hls.on(Hls.Events.MANIFEST_PARSED, () => {
          console.log('视频清单解析完成，可以播放')
        })

        hls.on(Hls.Events.ERROR, (event, data) => {
          console.error('HLS播放错误:', event, data)
        })
      } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
        // Safari原生支持HLS
        videoElement.src = videoUrl
      } else {
        console.error('浏览器不支持HLS播放')
      }
    } else {
      console.warn('视频URL为空')
    }
  } catch (error) {
    console.error('初始化视频播放器时出错:', error)
  }
}

const videoType = ref(null)
const getVideoTypeData = async () => {
  try {
    console.log('调用getVideoType接口获取视频类型数据')
    const result = await getVideoType()
    console.log('getVideoType接口返回结果:', result)

    if (result && result.code === 200 && result.data) {
      videoType.value = result.data
      console.log('视频类型数据更新成功:', videoType.value)
    } else if (result && result.rows) {
      // 有些接口可能返回rows字段
      videoType.value = result.rows
      console.log('视频类型数据更新成功(rows):', videoType.value)
    } else {
      console.warn('getVideoType接口返回数据格式异常:', result)
      videoType.value = []
    }
  } catch (error) {
    console.error('获取视频类型数据失败:', error)
    videoType.value = []
  }
}

// 视频
const videoMapData = ref(null)
const getVideoDataMap = async (params = { pageSize: 100 }) => {
  const result = await getVideo(params)
  if (result.code === 200) {
    videoMapData.value = result.rows
    console.log('视频数据', videoMapData.value)
  }
}

const ckData = ref(null)
const getCkDataDeails = async id => {
  if (!id) {
    console.error('应急物资ID不存在')
    return
  }
  try {
    const result = await getCkDataList(id)
    // console.log('设备详情数据1111:', result)

    if (result && result.code === 200) {
      ckData.value = result.data
      console.log('ckData.value', ckData.value)
    }
  } catch (error) {
    console.error('应急物资数据失败:', error)
    ckData.value = null
  }
}

// 应急物资
const yjwzData = ref(null)
const getCkData = async () => {
  const result = await getCkMap()
  if (result.code === 200) {
    yjwzData.value = result.rows
    if (yjwzData.value) {
      handleCk({ locationData: yjwzData.value })
    }
    console.log('仓库', yjwzData.value)
  }
}

// 人员定位
const rydw = ref()
const getRydwData = async () => {
  try {
    const result = await getRydw()
    if (result) {
      if (result.code === 200) {
        // 处理人员定位数据
        rydw.value = result.data
        console.log('人员定位数据:', rydw.value)

        // 更新 personnelType 中的计数
        updatePersonnelTypeCount()
      }
    }
  } catch (error) {
    console.error('获取人员定位数据失败:', error)
    console.log('使用默认人员定位数据')
    // 设置默认数据
    rydw.value = {
      total: 1593,
      online: 1,
      offline: 1592
    }

    // 更新 personnelType 中的计数
    updatePersonnelTypeCount()
  }
}

// 更新人员定位类型中的计数
const updatePersonnelTypeCount = () => {
  if (rydw.value) {
    // 更新全部
    const allItem = personnelType.value.find(item => item.status === 'all')
    if (allItem) {
      allItem.count = rydw.value.total || 0
    }

    // 更新在线
    const onlineItem = personnelType.value.find(item => item.status === '1')
    if (onlineItem) {
      onlineItem.count = rydw.value.online || 0
    }

    // 更新离线
    const offlineItem = personnelType.value.find(item => item.status === '0')
    if (offlineItem) {
      offlineItem.count = rydw.value.offline || 0
    }

    console.log('更新后的人员定位类型:', personnelType.value)
  }
}

// 人员定位类型
const personnelType = ref([
  { name: '全部', status: 'all', selected: true, count: 0 },
  { name: '在线', status: '1', selected: true, count: 0 },
  { name: '离线', status: '0', selected: true, count: 0 }
])

// 人员定位地图数据
const rydwMapData = ref([])

// 防汛泵车
const fxbc = ref()
// 防汛泵车类型
const pumpType = ref([
  { name: '全部', status: 'all', selected: true, count: 0 },
  { name: '在线', status: '1', selected: true, count: 0 },
  { name: '离线', status: '0', selected: true, count: 0 }
])

// 防汛泵车地图数据
const fxbcMapData = ref([])

// 其他视频数据
const otherVideoData = ref([])
const videoSearchText = ref('')
const selectedVideoCategory = ref('全部')

// 防汛简报相关状态
const floodReportVisible = ref(false)
const floodReportData = ref([])
const floodReportPageNum = ref(1)
const floodReportPageSize = ref(10)
const floodReportTotal = ref(0)

// 防抖定时器
let searchTimer = null

// 获取防汛简报数据
const getFloodReportData = async (params = {}) => {
  try {
    console.log('=== 开始调用getFloodReport接口 ===')
    const requestParams = {
      pageNum: params.pageNum || floodReportPageNum.value,
      pageSize: params.pageSize || floodReportPageSize.value
    }
    console.log('请求参数:', requestParams)

    const result = await getFloodReport(requestParams)
    console.log('接口返回结果:', result)

    if (result && result.code === 200) {
      floodReportData.value = result.rows || []
      floodReportTotal.value = result.total || 0
      console.log('更新后的防汛简报数据:', floodReportData.value)
      console.log('总数据量:', floodReportTotal.value)
    } else {
      console.warn('接口返回数据格式异常:', result)
      floodReportData.value = []
      floodReportTotal.value = 0
    }

    console.log('=== getFloodReport接口调用完成 ===')
  } catch (error) {
    console.error('获取防汛简报数据失败:', error)
    console.error('错误详情:', error.message)
    // 如果接口调用失败，使用模拟数据
    floodReportData.value = generateMockFloodReportData()
    floodReportTotal.value = floodReportData.value.length
  }
}

// 生成模拟防汛简报数据
const generateMockFloodReportData = () => {
  const mockData = [
    {
      id: 1,
      sequence: 1,
      title: '雨中宁河区(2025-8-1-16-0)',
      timeRange: '2025-08-01 13:00:07 -- 2025-08-01 16:00:07',
      reportTime: '2025-08-01 16:00:07'
    },
    {
      id: 2,
      sequence: 2,
      title: '雨后宁河区(2025-8-1-16-0)',
      timeRange: '2025-08-01 13:00:00 -- 2025-08-01 16:00:00',
      reportTime: '2025-08-01 16:00:00'
    },
    {
      id: 3,
      sequence: 3,
      title: '雨前宁河区(2025-8-1-16-0)',
      timeRange: '2025-08-01 13:00:00 -- 2025-08-01 16:00:00',
      reportTime: '2025-08-01 16:00:00'
    },
    {
      id: 4,
      sequence: 4,
      title: '雨中宁河区(2025-8-1-15-0)',
      timeRange: '2025-08-01 12:00:00 -- 2025-08-01 15:00:00',
      reportTime: '2025-08-01 15:00:00'
    },
    {
      id: 5,
      sequence: 5,
      title: '雨中宁河区(2025-8-1-14-0)',
      timeRange: '2025-08-01 11:00:06 -- 2025-08-01 14:00:06',
      reportTime: '2025-08-01 14:00:06'
    },
    {
      id: 6,
      sequence: 6,
      title: '雨后宁河区(2025-8-1-14-0)',
      timeRange: '2025-08-01 11:00:02 -- 2025-08-01 14:00:02',
      reportTime: '2025-08-01 14:00:02'
    },
    {
      id: 7,
      sequence: 7,
      title: '雨前宁河区(2025-8-1-14-0)',
      timeRange: '2025-08-01 11:00:01 -- 2025-08-01 14:00:01',
      reportTime: '2025-08-01 14:00:01'
    },
    {
      id: 8,
      sequence: 8,
      title: '雨中宁河区(2025-8-1-13-0)',
      timeRange: '2025-08-01 10:00:00 -- 2025-08-01 13:00:00',
      reportTime: '2025-08-01 13:00:00'
    },
    {
      id: 9,
      sequence: 9,
      title: '雨中宁河区(2025-8-1-12-0)',
      timeRange: '2025-08-01 09:00:05 -- 2025-08-01 12:00:05',
      reportTime: '2025-08-01 12:00:05'
    },
    {
      id: 10,
      sequence: 10,
      title: '雨前宁河区(2025-8-1-12-0)',
      timeRange: '2025-08-01 09:00:03 -- 2025-08-01 12:00:03',
      reportTime: '2025-08-01 12:00:03'
    }
  ]
  return mockData
}

// 关闭防汛简报弹窗
const closeFloodReportDialog = () => {
  floodReportVisible.value = false
}

// 处理防汛简报分页变化
const handleFloodReportPageChange = (page) => {
  floodReportPageNum.value = page
  getFloodReportData()
}

// 处理防汛简报页面大小变化
const handleFloodReportSizeChange = (size) => {
  floodReportPageSize.value = size
  floodReportPageNum.value = 1
  getFloodReportData()
}

// 处理预览操作
const handlePreview = (row) => {
  console.log('预览防汛简报:', row)
  // TODO: 实现预览功能，可以打开新窗口或显示详情弹窗
}

// 处理下载操作
const handleDownload = (row) => {
  console.log('下载防汛简报:', row)
  // TODO: 实现下载功能
}

// 获取其他视频数据
const getOtherVideoData = async (params = { pageSize: 100 }) => {
  try {
    console.log('=== 开始调用getVideo接口 ===')
    console.log('请求参数:', params)
    console.log('接口地址: /taiyuan/sxtxxb/list')

    const result = await getVideo(params)
    console.log('接口返回结果:', result)

    if (result && result.code === 200 && result.rows) {
      // 将接口返回的数据转换为我们需要的格式
      otherVideoData.value = result.rows
      console.log('更新后的视频数据:', otherVideoData.value)

      // 将视频数据传入handleVd函数
      console.log('调用handleVd处理视频数据，数据条数:', result.rows.length)
      handleVd({ locationData: result.rows })
    } else {
      console.warn('接口返回数据格式异常:', result)
      otherVideoData.value = []
      // 即使没有数据也调用handleVd，传入空数组
      console.log('调用handleVd处理空视频数据')
      handleVd({ locationData: [] })
    }

    console.log('=== getVideo接口调用完成 ===')
  } catch (error) {
    console.error('获取其他视频数据失败:', error)
    console.error('错误详情:', error.message)
    // 如果接口调用失败，可以显示错误提示或使用默认数据
    otherVideoData.value = []
    // 错误时也调用handleVd，传入空数组
    console.log('调用handleVd处理错误情况下的空视频数据')
    handleVd({ locationData: [] })
  }
}

// 获取防汛泵车统计数据
const getFxbcData = async () => {
  try {
    const result = await getFxbc()
    if (result) {
      if (result.code === 200) {
        // 处理防汛泵车数据
        fxbc.value = result.data
        console.log('防汛泵车数据:', fxbc.value)

        // 更新 pumpType 中的计数
        updatePumpTypeCount()
      }
    }
  } catch (error) {
    console.error('获取防汛泵车数据失败:', error)
    console.log('使用默认防汛泵车数据')
    // 设置默认数据
    fxbc.value = {
      total: 100,
      online: 80,
      offline: 20
    }

    // 更新 pumpType 中的计数
    updatePumpTypeCount()
  }
}

// 更新防汛泵车类型中的计数
const updatePumpTypeCount = () => {
  if (fxbc.value) {
    // 更新全部
    const allItem = pumpType.value.find(item => item.status === 'all')
    if (allItem) {
      allItem.count = fxbc.value.total || 0
    }

    // 更新在线
    const onlineItem = pumpType.value.find(item => item.status === '1')
    if (onlineItem) {
      onlineItem.count = fxbc.value.online || 0
    }

    // 更新离线
    const offlineItem = pumpType.value.find(item => item.status === '0')
    if (offlineItem) {
      offlineItem.count = fxbc.value.offline || 0
    }

    console.log('更新后的防汛泵车类型:', pumpType.value)
  }
}

// 获取防汛泵车地图数据
const getFxbcMapData = async (selectedStatuses = []) => {
  try {
    // 如果没有传入选中的状态，则获取当前选中的具体状态（排除"全部"）
    if (selectedStatuses.length === 0) {
      selectedStatuses = pumpType.value.filter(item => item.selected && item.status !== 'all').map(item => item.status)
    }

    // 如果没有选中任何具体状态，则不调用接口
    if (selectedStatuses.length === 0) {
      console.log('没有选中任何具体状态，清空数据')
      fxbcMapData.value = []
      return []
    }

    console.log('getFxbcMapData 获取防汛泵车地图数据，状态:', selectedStatuses)

    // 调用接口获取数据
    const promises = selectedStatuses.map(status => {
      console.log(`getFxbcMapData 调用接口 getFxbcMap(${status})`)
      return getFxbcMap(status).catch(error => {
        console.error(`getFxbcMapData 获取${status}状态泵车数据失败:`, error)
        return null
      })
    })

    const results = await Promise.all(promises)
    console.log('getFxbcMapData 接口返回结果:', results)

    // 合并所有结果
    let allData = []
    results.forEach((result, index) => {
      console.log(`getFxbcMapData 处理结果 ${index}:`, result)
      if (result && result.code === 200 && result.data) {
        console.log(`getFxbcMapData 结果 ${index} 数据:`, result.data)
        allData = allData.concat(result.data)
      }
    })

    fxbcMapData.value = allData
    console.log('getFxbcMapData 最终合并的防汛泵车地图数据:', fxbcMapData.value)
    console.log('getFxbcMapData 数据数量:', allData.length)

    // 自动调用handleBc显示标记点
    if (fxbcMapData.value && fxbcMapData.value.length > 0) {
      handleBc({ locationData: fxbcMapData.value })
    }

    return allData
  } catch (error) {
    console.error('获取防汛泵车地图数据失败:', error)
    return []
  }
}

// 获取人员定位地图数据
const getRydwMapData = async (selectedStatuses = []) => {
  try {
    // 如果没有传入选中的状态，则获取当前选中的具体状态（排除"全部"）
    if (selectedStatuses.length === 0) {
      selectedStatuses = personnelType.value
        .filter(item => item.selected && item.status !== 'all')
        .map(item => item.status)
    }

    // 如果没有选中任何具体状态，则不调用接口
    if (selectedStatuses.length === 0) {
      console.log('没有选中任何具体状态，清空数据')
      rydwMapData.value = []
      return []
    }

    console.log('getRydwMapData 获取人员定位地图数据，状态:', selectedStatuses)

    // 调用接口获取数据
    const promises = selectedStatuses.map(status => {
      console.log(`getRydwMapData 调用接口 getRydwMap(${status})`)
      return getRydwMap(status).catch(error => {
        console.error(`getRydwMapData 获取${status}状态人员数据失败:`, error)
        return null
      })
    })

    const results = await Promise.all(promises)
    console.log('getRydwMapData 接口返回结果:', results)

    // 合并所有结果
    let allData = []
    results.forEach((result, index) => {
      console.log(`getRydwMapData 处理结果 ${index}:`, result)
      if (result && result.code === 200 && result.data) {
        console.log(`getRydwMapData 结果 ${index} 数据:`, result.data)
        allData = allData.concat(result.data)
      }
    })

    rydwMapData.value = allData
    if (rydwMapData.value) {
      handlePeople({ locationData: rydwMapData.value })
    }
    console.log('getRydwMapData 最终合并的人员定位地图数据:', rydwMapData.value)
    console.log('getRydwMapData 数据数量:', allData.length)

    return allData
  } catch (error) {
    console.error('获取人员定位地图数据失败:', error)
    return []
  }
}

// 重点场景类型
const zdscType = ref([
  { sstype: '全部', selected: true },
  { sstype: '低洼路段', selected: true },
  { sstype: '人行下穿', selected: true }
])

// 重点场景数据
const zdscData = ref([])

// 获取重点场景类型列表
const getZdcjTypeList = async () => {
  try {
    const result = await getZdcjType()
    if (result) {
      if (result.code === 200) {
        // 只添加不重复的数据，并设置默认选中状态
        result.data.forEach(newItem => {
          const exists = zdscType.value.some(existingItem => existingItem.sstype === newItem.sstype)
          if (!exists && newItem.sstype !== '全部') {
            zdscType.value.push({
              sstype: newItem.sstype,
              selected: true // 默认选中
            })
          }
        })
        console.log('场景类型:', zdscType.value)
      }
    }
  } catch (error) {
    console.error('获取场景类型失败:', error)
    console.log('使用默认场景类型数据')
  }
}

// 获取重点场景数据
const getZdcjData = async (selectedTypes = []) => {
  try {
    // 如果没有传入选中的类型，则获取当前选中的具体类型（排除"全部"）
    if (selectedTypes.length === 0) {
      selectedTypes = zdscType.value.filter(item => item.selected && item.sstype !== '全部').map(item => item.sstype)
    }

    // 如果没有选中任何具体类型，则不调用接口
    if (selectedTypes.length === 0) {
      console.log('没有选中任何具体类型，清空数据')
      zdscData.value = []
      return []
    }

    console.log('获取重点场景数据，类型:', selectedTypes)

    // 调用接口获取数据
    const promises = selectedTypes.map(type => {
      return getZdcj(type).catch(error => {
        console.error(`获取${type}数据失败:`, error)
        return null
      })
    })

    const results = await Promise.all(promises)

    // 合并所有结果
    let allData = []
    results.forEach(result => {
      if (result && result.code === 200 && result.data) {
        allData = allData.concat(result.data)
      }
    })

    zdscData.value = allData
    if (zdscData.value) {
      handleScene({ locationData: zdscData.value })
    }
    console.log('重点场景数据:', zdscData.value)

    return allData
  } catch (error) {
    console.error('获取重点场景数据失败:', error)
    return []
  }
}

// 异常预警类型
const warningType = ref([
  { name: '全部', level: 'all', selected: true },
  { name: '一级预警', level: '一级', selected: true },
  { name: '二级预警', level: '二级', selected: true },
  { name: '三级预警', level: '三级', selected: true },
  { name: '四级预警', level: '四级', selected: true }
])

// 异常预警数据
const ycyjData = ref([])

// 获取异常预警数据
const getYcyjData = async (selectedLevels = []) => {
  try {
    // 如果没有传入选中的等级，则获取当前选中的具体等级（排除"全部"）
    if (selectedLevels.length === 0) {
      selectedLevels = warningType.value.filter(item => item.selected && item.level !== 'all').map(item => item.level)
    }

    // 如果没有选中任何具体等级，则不调用接口
    if (selectedLevels.length === 0) {
      console.log('没有选中任何具体等级，清空数据')
      ycyjData.value = []
      return []
    }

    console.log('获取异常预警数据，等级:', selectedLevels)

    // 调用接口获取数据
    const promises = selectedLevels.map(level => {
      return getYcyj(level).catch(error => {
        console.error(`获取${level}级预警数据失败:`, error)
        return null
      })
    })

    const results = await Promise.all(promises)

    // 合并所有结果
    let allData = []
    results.forEach(result => {
      if (result && result.code === 200 && result.data) {
        allData = allData.concat(result.data)
      }
    })

    ycyjData.value = allData
    console.log('异常预警数据:', ycyjData.value)
    if (ycyjData.value) {
      handleScene({ locationData: ycyjData.value })
    }
    return allData
  } catch (error) {
    console.error('获取异常预警数据失败:', error)
    return []
  }
}

// 右侧面板相关状态
const showRightPanel = ref(false)
const activeTabIndex = ref(-1)
const currentTabName = ref('')

// 中间标签配置
const middleTabItems = ref([
  { name: '气象云图', icon: '' },
  { name: '联勤联动', icon: '' },
  { name: '重点场景', icon: '' },
  { name: '异常预警', icon: '' },
  { name: '人员定位', icon: '' },
  { name: '防汛泵车', icon: '' },
  { name: '应急物资', icon: '' },
  { name: '其他视频', icon: '' },
  { name: '防汛简报', icon: '' }
])
// 标签页处理配置
const tabHandlers = {
  气象云图: () => {
    yuntuClickShowP(1) // 传递正确的flag参数
    console.log('气象云图功能已激活')
  },
  联勤联动: () => {
    // TODO: 实现联勤联动功能
    console.log('联勤联动功能待实现')
  },
  重点场景: () => {
    // 显示右侧面板并展示重点场景类型
    showRightPanel.value = true
    getZdcjTypeList()
    getZdcjData() // 获取场景数据

    console.log('重点场景功能已激活，显示右侧面板')
  },
  异常预警: () => {
    // 显示右侧面板并展示异常预警类型
    showRightPanel.value = true
    getYcyjData() // 获取异常预警数据
    console.log('异常预警功能已激活，显示右侧面板')
  },
  人员定位: () => {
    // 显示右侧面板并展示人员定位统计
    showRightPanel.value = true
    getRydwData() // 获取统计数据
    getRydwMapData() // 获取地图数据
    console.log('人员定位功能已激活，显示右侧面板')
  },
  防汛泵车: () => {
    // 显示右侧面板并展示防汛泵车统计
    showRightPanel.value = true
    getFxbcData() // 获取统计数据
    getFxbcMapData() // 获取地图数据
    console.log('防汛泵车功能已激活，显示右侧面板')
  },
  应急物资: () => {
    // TODO: 实现应急物资功能
    getCkData()
    console.log('应急物资功能待实现')
  },
  其他视频: () => {
    // 显示右侧面板并展示监控视频列表
    showRightPanel.value = true
    getVideoTypeData() // 获取视频类型数据
    getOtherVideoData({ pageSize: 100 }) // 获取视频数据，使用默认参数
    console.log('其他视频功能已激活，显示右侧面板')
  },
  防汛简报: () => {
    // 显示防汛简报弹窗
    floodReportVisible.value = true
    getFloodReportData()
    console.log('防汛简报功能已激活，显示弹窗')
  }
}

const changeTabs = (tabName, index) => {
  console.log('切换到:', tabName)

  // 参数验证
  if (!tabName || typeof tabName !== 'string') {
    console.error('无效的选项卡名称:', tabName)
    return
  }

  try {
    // 更新状态
    activeTabIndex.value = index
    currentTabName.value = tabName

    // 执行对应的处理函数
    const handler = tabHandlers[tabName]
    if (handler) {
      handler()
    } else {
      console.warn('未找到选项卡处理函数:', tabName)
    }
  } catch (error) {
    console.error('切换选项卡时发生错误:', error)
  }
}

// 关闭右侧面板
const closeRightPanel = () => {
  showRightPanel.value = false
  activeTabIndex.value = -1
  currentTabName.value = ''
}

// 处理列表项点击 - 重点场景选择/取消选择
const handleListItemClick = async item => {
  console.log('点击列表项:', item)

  if (item.sstype) {
    // 切换选中状态
    item.selected = !item.selected

    // 如果点击的是"全部"
    if (item.sstype === '全部') {
      // 如果选中"全部"，则选中所有具体类型
      if (item.selected) {
        zdscType.value.forEach(type => {
          if (type.sstype !== '全部') {
            type.selected = true
          }
        })
        // 勾选全部时，获取数据并调用handleScene
        const data = await getZdcjData()
        if (data && data.length > 0) {
          handleScene({ locationData: data })
        }
      } else {
        // 如果取消"全部"，则取消所有具体类型
        zdscType.value.forEach(type => {
          if (type.sstype !== '全部') {
            type.selected = false
          }
        })
        // 取消全部时，清除所有标记点
        fdapi.marker.clear()
      }
    } else {
      // 如果点击的是具体类型
      // 检查是否所有具体类型都被选中
      const specificTypes = zdscType.value.filter(type => type.sstype !== '全部')
      const allSpecificSelected = specificTypes.every(type => type.selected)

      // 更新"全部"的选中状态
      const allItem = zdscType.value.find(type => type.sstype === '全部')
      if (allItem) {
        allItem.selected = allSpecificSelected
      }

      // 每次取消或勾选其他选项时，获取数据并调用handleScene
      const data = await getZdcjData()
      if (data && data.length > 0) {
        handleScene({ locationData: data })
      } else {
        // 如果没有数据，清除标记点
        fdapi.marker.clear()
      }
    }

    console.log('更新后的场景类型选择状态:', zdscType.value)
  }
}

// 处理异常预警列表项点击
const handleWarningItemClick = async item => {
  console.log('点击异常预警项:', item)

  if (item.level) {
    // 切换选中状态
    item.selected = !item.selected

    // 如果点击的是"全部"
    if (item.level === 'all') {
      // 如果选中"全部"，则选中所有具体等级
      if (item.selected) {
        warningType.value.forEach(type => {
          if (type.level !== 'all') {
            type.selected = true
          }
        })
        // 勾选全部时，获取数据并调用handleScene
        const data = await getYcyjData()
        if (data && data.length > 0) {
          handleScene({ locationData: data })
        }
      } else {
        // 如果取消"全部"，则取消所有具体等级
        warningType.value.forEach(type => {
          if (type.level !== 'all') {
            type.selected = false
          }
        })
        // 取消全部时，清除所有标记点
        fdapi.marker.clear()
      }
    } else {
      // 如果点击的是具体等级
      // 检查是否所有具体等级都被选中
      const specificTypes = warningType.value.filter(type => type.level !== 'all')
      const allSpecificSelected = specificTypes.every(type => type.selected)

      // 更新"全部"的选中状态
      const allItem = warningType.value.find(type => type.level === 'all')
      if (allItem) {
        allItem.selected = allSpecificSelected
      }

      // 每次取消或勾选其他选项时，获取数据并调用handleScene
      const data = await getYcyjData()
      if (data && data.length > 0) {
        handleScene({ locationData: data })
      } else {
        // 如果没有数据，清除标记点
        fdapi.marker.clear()
      }
    }

    console.log('更新后的异常预警选择状态:', warningType.value)
  }
}

// 处理人员定位列表项点击
const handlePersonnelItemClick = async item => {
  console.log('点击人员定位项:', item)

  if (item.status) {
    // 切换选中状态
    item.selected = !item.selected

    // 如果点击的是"全部"
    if (item.status === 'all') {
      // 如果选中"全部"，则选中所有具体状态
      if (item.selected) {
        personnelType.value.forEach(type => {
          if (type.status !== 'all') {
            type.selected = true
          }
        })
        // 勾选全部时，获取数据并调用handlePeople
        const data = await getRydwMapData()
        if (data && data.length > 0) {
          handlePeople({ locationData: data })
        }
      } else {
        // 如果取消"全部"，则取消所有具体状态
        personnelType.value.forEach(type => {
          if (type.status !== 'all') {
            type.selected = false
          }
        })
        // 取消全部时，清除所有标记点
        fdapi.marker.clear()
      }
    } else {
      // 如果点击的是具体状态
      // 检查是否所有具体状态都被选中
      const specificTypes = personnelType.value.filter(type => type.status !== 'all')
      const allSpecificSelected = specificTypes.every(type => type.selected)

      // 更新"全部"的选中状态
      const allItem = personnelType.value.find(type => type.status === 'all')
      if (allItem) {
        allItem.selected = allSpecificSelected
      }

      // 每次取消或勾选其他选项时，获取数据并调用handlePeople
      const data = await getRydwMapData()
      if (data && data.length > 0) {
        handlePeople({ locationData: data })
      } else {
        // 如果没有数据，清除标记点
        fdapi.marker.clear()
      }
    }

    console.log('更新后的人员定位选择状态:', personnelType.value)
  }
}

// 处理防汛泵车列表项点击
const handlePumpItemClick = async item => {
  console.log('点击防汛泵车项:', item)

  if (item.status) {
    // 切换选中状态
    item.selected = !item.selected

    // 如果点击的是"全部"
    if (item.status === 'all') {
      // 如果选中"全部"，则选中所有具体状态
      if (item.selected) {
        pumpType.value.forEach(type => {
          if (type.status !== 'all') {
            type.selected = true
          }
        })
        // 勾选全部时，获取数据并调用handleBc
        const data = await getFxbcMapData()
        if (data && data.length > 0) {
          handleBc({ locationData: data })
        }
      } else {
        // 如果取消"全部"，则取消所有具体状态
        pumpType.value.forEach(type => {
          if (type.status !== 'all') {
            type.selected = false
          }
        })
        // 取消全部时，清除所有标记点
        fdapi.marker.clear()
      }
    } else {
      // 如果点击的是具体状态
      // 检查是否所有具体状态都被选中
      const specificTypes = pumpType.value.filter(type => type.status !== 'all')
      const allSpecificSelected = specificTypes.every(type => type.selected)

      // 更新"全部"的选中状态
      const allItem = pumpType.value.find(type => type.status === 'all')
      if (allItem) {
        allItem.selected = allSpecificSelected
      }

      // 每次取消或勾选其他选项时，获取数据并调用handleBc
      const data = await getFxbcMapData()
      if (data && data.length > 0) {
        handleBc({ locationData: data })
      } else {
        // 如果没有数据，清除标记点
        fdapi.marker.clear()
      }
    }

    console.log('更新后的防汛泵车选择状态:', pumpType.value)
  }
}

// 处理视频搜索（实时搜索，带防抖）
const handleVideoSearch = () => {
  console.log('触发实时搜索，搜索文本:', videoSearchText.value)

  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  // 设置新的定时器，500ms后执行搜索
  searchTimer = setTimeout(async () => {
    console.log('执行实时搜索，搜索文本:', videoSearchText.value)
    console.log('当前选择分类:', selectedVideoCategory.value)

    try {
      // 构建查询参数
      const params = {
        pageSize: 100 // 默认分页大小
      }

      // 如果有搜索文本，添加到参数中
      if (videoSearchText.value.trim()) {
        params.sxtmc = videoSearchText.value.trim()
      }

      // 如果选择了特定分类，添加到参数中
      if (selectedVideoCategory.value !== '全部') {
        params.yycjmc = selectedVideoCategory.value
      }

      console.log('实时搜索参数:', params)

      // 重新调用接口获取数据
      await getOtherVideoData(params)

      console.log('实时搜索完成，数据已更新')
    } catch (error) {
      console.error('实时搜索失败:', error)
    }
  }, 500)
}

// 处理查询按钮点击
const handleVideoSearchClick = async () => {
  console.log('点击查询按钮，搜索视频:', videoSearchText.value)
  console.log('当前选择的分类:', selectedVideoCategory.value)

  try {
    // 构建查询参数
    const params = {
      pageSize: 100 // 默认分页大小
    }

    // 如果有搜索文本，添加到参数中
    if (videoSearchText.value.trim()) {
      // 根据接口文档，这里可能需要调整参数名
      params.sxtmc = videoSearchText.value.trim() // 摄像头名称搜索
    }

    // 如果选择了特定分类，添加到参数中
    if (selectedVideoCategory.value !== '全部') {
      // 使用yycjmc字段作为应用场景名称筛选参数
      params.yycjmc = selectedVideoCategory.value // 应用场景名称筛选
    }

    console.log('查询参数:', params)

    // 调用接口获取数据
    await getOtherVideoData(params)

    // 查询完成后的反馈
    if (otherVideoData.value.length === 0) {
      console.log('未找到匹配的视频')
    } else {
      console.log(`找到 ${otherVideoData.value.length} 个匹配的视频`)
    }
  } catch (error) {
    console.error('查询视频失败:', error)
  }
}

// 处理分类下拉框变化
const handleCategoryChange = async () => {
  console.log('选择分类:', selectedVideoCategory.value)
  console.log('当前videoType数据:', videoType.value)
  console.log(
    '下拉框所有选项:',
    videoType.value?.map(item => item.yycjmc)
  )

  try {
    // 构建查询参数
    const params = {
      pageSize: 100 // 默认分页大小
    }

    // 如果有搜索文本，添加到参数中
    if (videoSearchText.value.trim()) {
      params.sxtmc = videoSearchText.value.trim()
    }

    // 如果选择了特定分类，添加到参数中
    if (selectedVideoCategory.value !== '全部') {
      params.yycjmc = selectedVideoCategory.value
    }

    console.log('分类筛选参数:', params)

    // 调用接口获取数据
    await getOtherVideoData(params)
  } catch (error) {
    console.error('分类筛选失败:', error)
  }
}

// 处理视频项点击
const handleVideoItemClick = video => {
  console.log('点击视频项:', video)

  if (video.status === 'offline') {
    console.log('视频离线，无法播放')
    // 可以显示提示信息
    return
  }

  // 这里可以实现视频播放功能
  // 例如打开视频播放窗口或跳转到视频页面
  console.log('播放视频:', video.name, video.id)
}

const hsfqData = ref(null)
const gethsfq = async yldName => {
  const gethsfq = async yldName => {
    const res = await getyldfq(yldName)
    if (res.code === 200) {
      hsfqData.value = res.rows[0]
      console.log('汇水分区', hsfqData.value)
      console.log('汇水分区', hsfqData.value)
    }
  }
}
const callphonePreviewVisible = ref(false)
const closecallphonePreview = () => {
  callphonePreviewVisible.value = false
}
const router = useRouter()
const phoneNumber = ref('') // 示例电话号码

// 处理拨号功能 - 在新页面打开
const handlePhoneCall = phone => {
  if (!phone) {
    console.warn('电话号码为空')
    return
  }
  phoneNumber.value = phone
  callphonePreviewVisible.value = true
  // 导航到CallPhone页面并传递电话号码参数
  // router.push({
  //   name: 'callPhone',
  //   query: {
  //     phoneNum: phone
  //   }
  // })
}
// 视频信息
const vdPreviewVisible = ref(false)
const closeVdPreview = () => {
  console.log('关闭视频预览弹窗')

  // 清理视频播放器
  const videoElement = document.getElementById('car-video-player')
  if (videoElement) {
    videoElement.pause()
    videoElement.src = ''
    videoElement.load()
    console.log('视频播放器已清理')
  }

  // 清理视频详情数据
  vdDetails.value = null

  // 关闭弹窗
  vdPreviewVisible.value = false
}

// 仓库信息
const ckPreviewVisible = ref(false)
const closeCkPreview = () => {
  ckPreviewVisible.value = false
}

// 防汛人员信息
const peoplePreviewVisible = ref(false)
const closePeoplePreview = () => {
  peoplePreviewVisible.value = false
}

const tabsCctiveName = ref('bz')
// 防汛车辆信息
const carPreviewVisible = ref(false)
const closeCarPreview = () => {
  carPreviewVisible.value = false
}
const tablePreviewVisible = ref(false)

const closeTablePreview = () => {
  tablePreviewVisible.value = false
}
//  防汛车辆
const getCarDataDeails = async id => {
  if (!id) {
    console.error('设备ID不存在')
    return
  }

  try {
    const result = await getCarData(id)
    // console.log('设备详情数据1111:', result)

    if (result && result.code === 200) {
      deviceData.value = result.rows[0]
      console.log('deviceData.value', deviceData.value)
    } else {
      console.error('获取防汛数据失败或数据为空,调用应急物资数据')
      deviceData.value = null
    }
  } catch (error) {
    console.error('获取设备详情失败:', error)
    deviceData.value = null
  }
}
// 应急泵车
const yjbcPreviewVisible = ref(false)
const closeyjbcPreview = () => {
  yjbcPreviewVisible.value = false
}
//  应急泵车
const getYJBCDataDeails = async id => {
  if (!id) {
    console.error('设备ID不存在')
    return
  }

  try {
    const result = await getCarData(id)
    // console.log('设备详情数据1111:', result)

    if (result && result.code === 200) {
      deviceData.value = result.rows[0]
      console.log('deviceData.value', deviceData.value)
    } else {
      console.error('获取防汛数据失败或数据为空,调用应急物资数据')
      deviceData.value = null
    }
  } catch (error) {
    console.error('获取设备详情失败:', error)
    deviceData.value = null
  }
}

// 物资

const wzPreviewVisible = ref(false)
const closewzPreview = () => {
  wzPreviewVisible.value = false
}
//  应急泵车
const getWzDataDetails = async id => {
  if (!id) {
    console.error('设备ID不存在')
    return
  }
  yingjiwuziRootTable(cbdIdValue.value, yjwzPageNum.value, yjwzPageSize.value).then(res => {
    console.log(res, 'RootTable')
    yjwzdialogVisible.value = true
    if (res.code === 200) {
      yjwztableData.value = res.rows
      yjwzTotal.value = res.total
    }
  })
}

const deviceDialogVisible = ref(false)
const chart1Ref = ref(null)
const chart2Ref = ref(null)
let chart1Instance = null
let chart2Instance = null

// 图片预览相关变量
const imagePreviewVisible = ref(false)
const imagePreviewSrc = ref('')
const imagePreviewTitle = ref('')

// 图片预览方法
const openImagePreview = (imageSrc, title = '图片预览') => {
  imagePreviewSrc.value = imageSrc
  imagePreviewTitle.value = title
  imagePreviewVisible.value = true
}

const closeImagePreview = () => {
  imagePreviewVisible.value = false
  imagePreviewSrc.value = ''
  imagePreviewTitle.value = ''
}

// 键盘事件处理
const handleKeydown = event => {
  if (event.key === 'Escape' && imagePreviewVisible.value) {
    closeImagePreview()
  }
}

const handledeviceClose = () => {
  deviceDialogVisible.value = false
  // 销毁图表实例
  if (chart1Instance) {
    chart1Instance.dispose()
    chart1Instance = null
  }
  if (chart2Instance) {
    chart2Instance.dispose()
    chart2Instance = null
  }
}

// 初始化图表
const initCharts = async () => {
  // await nextTick()

  if (chart1Ref.value) {
    chart1Instance = echarts.init(chart1Ref.value)

    // 处理水位数据
    let xAxisData = []
    let seriesData = []
    let yAxisMin = 0
    let yAxisMax = 10

    if (waterLevelData.value && waterLevelData.value.length > 0) {
      // 从waterLevelData中提取时间和水位数据
      xAxisData = waterLevelData.value.map(item => {
        // 格式化时间显示，只显示时:分:秒
        const date = new Date(item.time)
        return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(
          date.getSeconds()
        ).padStart(2, '0')}`
      })

      seriesData = waterLevelData.value.map(item => parseFloat(item.waterlevel) || 0)

      // 动态计算Y轴范围
      if (seriesData.length > 0) {
        const minValue = Math.min(...seriesData)
        const maxValue = Math.max(...seriesData)
        const range = maxValue - minValue
        yAxisMin = Math.max(0, minValue - range * 0.1) // 最小值向下扩展10%，但不小于0
        yAxisMax = maxValue + range * 0.1 // 最大值向上扩展10%
      }
    } else {
      // 默认数据（当没有水位数据时）
      xAxisData = ['03:49:00', '05:07:00', '05:03:00', '05:10:00', '05:12:00']
      seriesData = [0, 0, 0, 0, 0]
    }

    const option1 = {
      backgroundColor: 'transparent',
      grid: {
        left: '10%',
        right: '5%',
        top: '5%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 24,
          margin: 8,
          // rotate: 45, // 旋转标签以避免重叠
          formatter: function (value) {
            // 如果标签太长，可以进一步简化
            return value
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '',
        nameTextStyle: {
          color: '#ffffff',
          fontSize: 24,
          show: false
        },
        // min: yAxisMin,
        // max: yAxisMax,
        min: 0,
        max: 10,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 24
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.2)',
            width: 1,
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '水位',
          data: seriesData,
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#00ff88',
            width: 2
          },
          itemStyle: {
            color: '#00ff88',
            borderColor: '#00ff88',
            borderWidth: 2
          },
          symbol: 'circle',
          symbolSize: 4,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(0, 255, 136, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(0, 255, 136, 0.05)'
                }
              ]
            }
          }
        }
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00ff88',
        borderWidth: 1,
        textStyle: {
          color: '#ffffff'
        },
        formatter: function (params) {
          if (params && params.length > 0) {
            const data = params[0]
            return `时间: ${data.axisValue}<br/>水位: ${data.value} cm`
          }
          return ''
        }
      }
    }
    chart1Instance.setOption(option1)
  }

  if (chart2Ref.value) {
    chart2Instance = echarts.init(chart2Ref.value)
    const option2 = {
      backgroundColor: 'transparent',
      grid: {
        left: '0%',
        right: '10%',
        top: '5%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['尖草坪区', '杏花岭区', '小店区', '迎泽区', '万柏林区'],
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 24,
          margin: 10
        }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 150,
        interval: 30,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 24
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.2)',
            width: 1,
            type: 'dashed'
          }
        }
      },
      series: [
        {
          data: [80, 60, 90, 110, 80, 70, 100, 90, 70, 110],
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#00ff88',
            width: 2
          },
          itemStyle: {
            color: '#00ff88',
            borderColor: '#00ff88',
            borderWidth: 2
          },
          symbol: 'circle',
          symbolSize: 4,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(0, 255, 136, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(0, 255, 136, 0.05)'
                }
              ]
            }
          }
        }
      ]
    }
    chart2Instance.setOption(option2)
  }
}

// 更新图表数据
const updateChart1WithWaterLevel = () => {
  if (!chart1Instance || !waterLevelData.value) return

  // 处理水位数据
  let xAxisData = []
  let seriesData = []
  let yAxisMin = 0
  let yAxisMax = 10

  if (waterLevelData.value && waterLevelData.value.length > 0) {
    // 从waterLevelData中提取时间和水位数据
    xAxisData = waterLevelData.value.map(item => {
      // 格式化时间显示，只显示时:分:秒
      const date = new Date(item.time)
      return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(
        date.getSeconds()
      ).padStart(2, '0')}`
    })

    seriesData = waterLevelData.value.map(item => parseFloat(item.waterlevel) || 0)

    // 动态计算Y轴范围
    if (seriesData.length > 0) {
      const minValue = Math.min(...seriesData)
      const maxValue = Math.max(...seriesData)
      const range = maxValue - minValue
      yAxisMin = Math.max(0, minValue - range * 0.1) // 最小值向下扩展10%，但不小于0
      yAxisMax = maxValue + range * 0.1 // 最大值向上扩展10%
    }
  } else {
    // 默认数据（当没有水位数据时）
    xAxisData = ['03:49:00', '05:07:00', '05:03:00', '05:10:00', '05:12:00']
    seriesData = [0, 0, 0, 0, 0]
  }

  // 更新图表配置
  chart1Instance.setOption({
    xAxis: {
      data: xAxisData
    },
    yAxis: {
      // min: yAxisMin,
      // max: yAxisMax
      min: 0,
      max: 150
    },
    series: [
      {
        data: seriesData
      }
    ]
  })
}

const yjwzdialogVisible = ref(false)
const yjwztableData = ref()
const yjwzPageNum = ref(1)
const yjwzPageSize = ref(20)
const yjwzTotal = ref(0)
const cbdIdValue = ref('')
// 地图实例
const mapRef = ref(null)
const progressValue = ref(0)
const twoDWaterSliderMarks = ref([
  '00:10:00',
  '00:20:00',
  '00:30:00',
  '00:40:00',
  '00:50:00',
  '01:00:00',
  '01:10:00',
  '01:20:00',
  '01:30:00',
  '01:40:00',
  '01:50:00'
])

const dialogVisibleYuntu = ref(false)
const yuntuClickShowP = flag => {
  dialogVisibleYuntu.value = true
  middleImgShow.value = flag
}

const middleImgShow = ref(1)

// iframe加载处理函数
const handleIframeLoad = () => {
  console.log('云图iframe加载成功')
}

const handleIframeError = () => {
  console.error('云图iframe加载失败')
}

// 关闭云图弹窗
const closeYuntuDialog = () => {
  dialogVisibleYuntu.value = false
  middleImgShow.value = null
}

// 点击遮罩层关闭弹窗
const handleOverlayClick = event => {
  console.log('handleOverlayClick 被触发', event.target, event.currentTarget)

  // 方法1: 检查点击的是遮罩层本身（最可靠的方法）
  if (event.target === event.currentTarget) {
    console.log('点击了遮罩层，关闭弹窗')
    closeYuntuDialog()
    return
  }

  // 方法2: 检查是否包含遮罩层类名（备用方案）
  if (event.target.classList.contains('yuntu-iframe-dialog-overlay')) {
    console.log('通过类名检查，关闭弹窗')
    closeYuntuDialog()
    return
  }

  console.log('点击的不是遮罩层，不关闭弹窗')
}

// 简单的关闭弹窗方法（备用方案）
const handleSimpleOverlayClick = () => {
  console.log('简单关闭弹窗方法被调用')
  closeYuntuDialog()
}
// 控制弹窗显示
const dialogVisible = ref(false)

// 存储鼠标点击位置的响应式变量
const mousePosition = ref({
  x: 0,
  y: 0,
  timestamp: null
})

// 控制点击指示器的显示
const showClickIndicator = ref(false)

// 处理全局鼠标点击事件
const handleGlobalClick = event => {
  clickX.value = event.clientX
  clickY.value = event.clientY
  console.log('全局鼠标点击位置:', clickX.value, clickY.value)
  // dialogVisible.value = true
  // 显示点击指示器
  showClickIndicator.value = true

  // 1秒后隐藏点击指示器
  setTimeout(() => {
    showClickIndicator.value = false
  }, 1000)
}

const activeNavButton = ref(0)
const currentShikeValue = ref('')
const hdlStart = () => {
  progressValue.value = 0
  let i = 1
  var interval = setInterval(() => {
    console.log(progressValue.value, 'progressValue.value')
    progressValue.value = i
    if (i <= twoDWaterSliderMarks.value.length) {
      currentShikeValue.value = twoDWaterSliderMarks.value[i]
    }
    handleProgressChange(i++)
    if (i >= twoDWaterSliderMarks.value.length) {
      clearInterval(interval)
    }
  }, 3000)
}
// 处理进度条变化
const handleProgressChange = value => {
  console.log(value, 'value')
  update2DWater(value)
}
// 随时间节点变化更新
const update2DWater = async value => {
  // 获取对应的时间字符串
  const timeStr = String(twoDWaterSliderMarks.value[value])

  const formattedTime = timeStr.replace(/:/g, '')

  let hydrodynamicModel_for_update = {
    id: 'hdm_shp_clip',
    updateTime: 1,
    shpDataFilePath: `@path:screenfile/water2D/dat/40mm/hydrodynamic_20250416_${formattedTime}.dat`
  }
  console.log(`@path:water2D/dat/40mm/hydrodynamic_20250416_${formattedTime}.dat`)
  console.log(hydrodynamicModel_for_update, 'hydrodynamicModel_for_update')
  await fdapi.hydrodynamic2d.update(hydrodynamicModel_for_update)
}
// 导航栏配置
const navItems = ref([{ name: '综合展示' }, { name: '报警演案' }, { name: '智能感知' }, { name: '辅助决策' }])

// 处理弹窗关闭事件
const handleClose = () => {
  console.log('弹窗关闭')
  dialogVisible.value = false
  // 确保关闭后重置设备数据
  deviceData.value = null
  singleMarkerData.value = null
}

// 切换导航
const changeNav = index => {
  console.log(index, '切换index')
  activeNavButton.value = index
  fdapi.marker.clear()
  fdapi.weather.disableRainSnow()
  if (index === 1) {
    // 报警演案加载地图水淹
    init2DWater()
  } else {
    // 移除地图水淹
    fdapi.hydrodynamic2d.clear()
  }
}

const init2DWater = async () => {
  fdapi.hydrodynamic2d.clear()
  let hydrodynamic2d_add = {
    id: 'hdm_shp_clip', // HydroDynamic2D对象ID
    displayMode: 1, // 水流场样式： 0水面 1热力 2流场
    waterMode: 0, // 水面显示模式，枚举类型 0 水动画模式 ,1水仿真模式,2 水流向模式
    // offset: [0, 0, -18], //整体水面高度修正
    collision: false, //开启碰撞
    updateTime: 0,
    shpFilePath: '@path:screenfile/water2D/shp/processed_grid.shp', //预加载的水动力模型预演范围shp
    colors: {
      gradient: false,
      invalidColor: [0, 0, 0, 0],
      colorStops: [
        { value: 0, color: [0.22, 0.659, 0.0, 1] },
        { value: 0.15, color: [0.298, 0.902, 0.0, 1] },
        { value: 0.27, color: [1.0, 1.0, 0.0, 1] },
        { value: 0.4, color: [1.0, 0.667, 0.0, 1] },
        { value: 0.6, color: [1.0, 0.0, 0.0, 1] }
      ]
    }
  }
  await fdapi.hydrodynamic2d.add(hydrodynamic2d_add)
  //fdapi.hydrodynamic2d.focus('hdm_shp_clip', 1000);  //飞入 调试时可用
  let hydrodynamicModel_for_update = {
    id: 'hdm_shp_clip',
    updateTime: 3,
    shpDataFilePath: '@path:screenfile/water2D/dat/40mm/hydrodynamic_20250416_001000.dat'
  }
  fdapi.hydrodynamic2d.update(hydrodynamicModel_for_update)

  console.log('-------加载完成---------')
}
const tagDataList = ref(null)

// 应急物资
// 处理子组件传来的标签数据
const handleGoods = data => {
  tagDataList.value = data
  console.log('rootMap接收到的数据:', data)
  // 这里可以处理接收到的数据，例如在地图上标记位置等
  if (data) {
    // 处理位置数据
    const locations = data.locationData
    const tagData = data.tagData
    console.log(locations, 'locations')

    // 验证 locations 是否为数组
    if (!Array.isArray(locations)) {
      console.error('locations 不是数组:', locations)
      return
    }

    // 可以在地图上标记这些位置
    try {
      // 先清除现有标记
      fdapi.marker.clear()

      // 遍历位置数据并添加标记
      let markers = []
      locations.forEach((location, index) => {
        console.log(location, 'location111111111111111111111')
        if (location.xcoordinate && location.ycoordinate) {
          // 确保有ID，如果没有则生成一个唯一ID
          const markerId = location.id || `goods_marker_${index}_${Date.now()}`

          // 创建标记配置
          const goodsMarker = {
            id: markerId,
            groupId: 'goodsLocations',
            coordinate: [parseFloat(location.xcoordinate), parseFloat(location.ycoordinate), 0],
            coordinateType: 1, // 经纬度坐标系
            anchors: [-25, 50],
            imageSize: [30, 30],
            hoverImageSize: [30, 30],
            range: [100, 10000000],
            viewHeightRange: [100, 10000000],
            rangeRatio: 0.01,
            imagePath: '@path:screenfile/zhtb/cl.png',
            hoverImagePath: '@path:screenfile/zhtb/cl.png',
            fixedSize: true,
            text: location.category,
            useTextAnimation: false,
            textRange: [1, 1000],
            textOffset: [0, 0],
            fontSize: 24,
            fontColor: window.Color ? window.Color.White : '#FFFFFF',
            fontOutlineColor: window.Color ? window.Color.Black : '#000000',
            showLine: true,
            lineSize: [2, 100],
            lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
            displayMode: 2
          }
          console.log(goodsMarker, 'goodsMarker')

          markers.push(goodsMarker)
        }
      })

      // 只有当markers数组不为空时才添加标记和聚焦
      if (markers.length > 0) {
        console.log(markers, '最终的打点数据')

        fdapi.marker.add(markers)
        fdapi.marker.focus(markers[0].id, 1200, 0)
        console.log('标记点添加成功，总数:', markers.length)
      } else {
        console.log('没有有效的位置数据可添加标记')
      }
    } catch (error) {
      console.error('添加标记点失败:', error)
    }
  }
}
// 处理打点-经纬度分开版-防汛车辆
const handleTagDataBranch = data => {
  tagDataList.value = data
  console.log('rootMap接收到的数据:', data)
  // 这里可以处理接收到的数据，例如在地图上标记位置等
  if (data) {
    // 处理位置数据
    const locations = data.locationData
    console.log(locations, 'locations')

    // 验证 locations 是否为数组
    if (!Array.isArray(locations)) {
      console.error('locations 不是数组:', locations)
      return
    }

    // 可以在地图上标记这些位置
    try {
      // 先清除现有标记
      fdapi.marker.clear()

      // 遍历位置数据并添加标记
      let markers = []
      locations.forEach((location, index) => {
        console.log(location, 'location111111111111111111111')
        if (location.jd && location.wd) {
          // 确保有ID，如果没有则生成一个唯一ID
          // 防汛车辆
          const markerId = 'FXCL_' + (location.sbbh || `${index}_${Date.now()}`)
          // 创建标记配置
          const markerCar = {
            id: markerId,
            groupId: 'tagLocations',
            coordinate: [parseFloat(location.jd), parseFloat(location.wd)],
            coordinateType: 1, // 经纬度坐标系
            anchors: [-15, 30],
            imageSize: [30, 30],
            hoverImageSize: [30, 30],
            range: [100, 10000000],
            viewHeightRange: [100, 10000000],
            rangeRatio: 0.01,
            imagePath: '@path:screenfile/zhtb/cl.png',
            hoverImagePath: '@path:screenfile/zhtb/cl.png',
            fixedSize: true,
            text: location.cphm,
            useTextAnimation: true,
            textRange: [1, 1000000000],
            textOffset: [0, 0],
            fontSize: 24,
            fontColor: window.Color ? window.Color.White : '#FFFFFF',
            fontOutlineColor: window.Color ? window.Color.Black : '#000000',
            showLine: true,
            lineSize: [2, 100],
            lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
            displayMode: 4,
            autoHeight: true
          }
          console.log(markerCar, 'marker')

          markers.push(markerCar)
        }
      })

      // 只有当markers数组不为空时才添加标记和聚焦
      if (markers.length > 0) {
        console.log(markers, '最终的打点数据')

        fdapi.marker.add(markers)
        fdapi.marker.focus(markers[0].id, 1200, 0)
        console.log('标记点添加成功，总数:', markers.length)
      } else {
        console.log('没有有效的位置数据可添加标记')
      }
    } catch (error) {
      console.error('添加标记点失败:', error)
    }
  }
}

const diaoduzhiliangData = ref([])
const diaoduzhilingtongjiVisible = ref(false)
const diaoduzhilingObject = ref({})
// 调度指令统计根据经纬度打点
const handleDdCommand = data => {
  diaoduzhiliangData.value = data.locationData
  console.log('rootMap接收到的数据:', data)
  // 这里可以处理接收到的数据，例如在地图上标记位置等
  if (data) {
    // 处理位置数据
    const locations = data.locationData
    console.log(locations, 'locations')

    // 验证 locations 是否为数组
    if (!Array.isArray(locations)) {
      console.error('locations 不是数组:', locations)
      return
    }

    // 可以在地图上标记这些位置
    try {
      // 先清除现有标记
      fdapi.marker.clear()

      // 遍历位置数据并添加标记
      let markers = []
      locations.forEach((location, index) => {
        if (location.xcoordinate && location.ycoordinate) {
          // 确保有ID，如果没有则生成一个唯一ID
          // 防汛车辆
          const markerId = 'DDZL_' + (location.dataIndex || `${index}_${Date.now()}`)
          // 创建标记配置
          const markerCar = {
            id: markerId,
            groupId: 'tagLocations',
            coordinate: [parseFloat(location.xcoordinate), parseFloat(location.ycoordinate)],
            coordinateType: 1, // 经纬度坐标系
            anchors: [-15, 30],
            imageSize: [30, 30],
            hoverImageSize: [30, 30],
            range: [100, 10000000],
            viewHeightRange: [100, 10000000],
            rangeRatio: 0.01,
            imagePath: '@path:screenfile/zhtb/cl.png',
            hoverImagePath: '@path:screenfile/zhtb/cl.png',
            fixedSize: true,
            text: location.command_type + '-' + location.adjustment_type,
            useTextAnimation: true,
            textRange: [1, 1000000000],
            textOffset: [0, 0],
            fontSize: 24,
            fontColor: window.Color ? window.Color.White : '#FFFFFF',
            fontOutlineColor: window.Color ? window.Color.Black : '#000000',
            showLine: true,
            lineSize: [2, 100],
            lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
            displayMode: 4,
            autoHeight: true
          }
          console.log(markerCar, 'marker')

          markers.push(markerCar)
        }
      })

      // 只有当markers数组不为空时才添加标记和聚焦
      if (markers.length > 0) {
        console.log(markers, '最终的打点数据')
        fdapi.marker.add(markers)
        if (data.index !== undefined) {
          // 点击调度指令切换条件
          fdapi.marker.focus(markers[data.index].id, 1200, 0)
        } else {
          // 不点击调度指令切换条件，直接点击下面表格的内容
          fdapi.marker.focus(markers[0].id, 1200, 0)
        }
        console.log('标记点添加成功，总数:', markers.length)
      } else {
        console.log('没有有效的位置数据可添加标记')
      }
    } catch (error) {
      console.error('添加标记点失败:', error)
    }
  }
}

// 处理物资打点
const handleWz = data => {
  tagDataList.value = data
  console.log('rootMap接收到的数据:', data)
  // 这里可以处理接收到的数据，例如在地图上标记位置等
  if (data) {
    // 处理位置数据
    const locations = data.locationData
    console.log(locations, 'locations')

    // 验证 locations 是否为数组
    if (!Array.isArray(locations)) {
      console.error('locations 不是数组:', locations)
      return
    }

    // 可以在地图上标记这些位置
    try {
      // 先清除现有标记
      fdapi.marker.clear()

      // 遍历位置数据并添加标记
      let markers = []
      locations.forEach((location, index) => {
        console.log(location, 'location111111111111111111111')
        if (location.xcoordinate && location.ycoordinate) {
          // 确保有ID，如果没有则生成一个唯一ID
          // 防汛车辆
          const markerId = 'WZ_' + (location.cbdId || `${index}_${Date.now()}`)
          // 创建标记配置
          const markerCar = {
            id: markerId,
            groupId: 'wzLocations',
            coordinate: [parseFloat(location.xcoordinate), parseFloat(location.ycoordinate)],
            coordinateType: 1, // 经纬度坐标系
            anchors: [-15, 30],
            imageSize: [30, 30],
            hoverImageSize: [30, 30],
            range: [100, 10000000],
            viewHeightRange: [100, 10000000],
            rangeRatio: 0.01,
            imagePath: '@path:screenfile/zhtb/phq.png',
            hoverImagePath: '@path:screenfile/zhtb/phq.png',
            fixedSize: true,
            text: location.ckname,
            useTextAnimation: true,
            textRange: [1, 1000000000],
            textOffset: [0, 0],
            fontSize: 24,
            fontColor: window.Color ? window.Color.White : '#FFFFFF',
            fontOutlineColor: window.Color ? window.Color.Black : '#000000',
            showLine: true,
            lineSize: [2, 100],
            lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
            displayMode: 4,
            autoHeight: true
          }
          console.log(markerCar, 'marker')

          markers.push(markerCar)
        }
      })

      // 只有当markers数组不为空时才添加标记和聚焦
      if (markers.length > 0) {
        console.log(markers, '最终的打点数据')

        fdapi.marker.add(markers)
        fdapi.marker.focus(markers[0].id, 1200, 0)
        console.log('标记点添加成功，总数:', markers.length)
      } else {
        console.log('没有有效的位置数据可添加标记')
      }
    } catch (error) {
      console.error('添加标记点失败:', error)
    }
  }
}

// 处理泵车打点
const handleBc = data => {
  tagDataList.value = data
  console.log('rootMap接收到的数据:', data)
  // 这里可以处理接收到的数据，例如在地图上标记位置等
  if (data) {
    // 处理位置数据
    const locations = data.locationData
    console.log(locations, 'locations')

    // 验证 locations 是否为数组
    if (!Array.isArray(locations)) {
      console.error('locations 不是数组:', locations)
      return
    }

    // 可以在地图上标记这些位置
    try {
      // 先清除现有标记
      fdapi.marker.clear()

      // 遍历位置数据并添加标记
      let markers = []
      locations.forEach((location, index) => {
        console.log(location, 'location111111111111111111111')
        if (location.jd && location.wd) {
          // 确保有ID，如果没有则生成一个唯一ID
          // 防汛车辆
          const markerId = 'YJBC_' + (location.sbbh || `${index}_${Date.now()}`)
          // 创建标记配置
          const markerCar = {
            id: markerId,
            groupId: 'bcLocations',
            coordinate: [parseFloat(location.jd), parseFloat(location.wd)],
            coordinateType: 1, // 经纬度坐标系
            anchors: [-15, 30],
            imageSize: [30, 30],
            hoverImageSize: [30, 30],
            range: [100, 10000000],
            viewHeightRange: [100, 10000000],
            rangeRatio: 0.01,
            imagePath: '@path:screenfile/zhtb/bc.png',
            hoverImagePath: '@path:screenfile/zhtb/bc.png',
            fixedSize: true,
            text: location.cphm,
            useTextAnimation: true,
            textRange: [1, 1000000000],
            textOffset: [0, 0],
            fontSize: 24,
            fontColor: window.Color ? window.Color.White : '#FFFFFF',
            fontOutlineColor: window.Color ? window.Color.Black : '#000000',
            showLine: true,
            lineSize: [2, 100],
            lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
            displayMode: 4,
            autoHeight: true
          }
          console.log(markerCar, 'marker')

          markers.push(markerCar)
        }
      })

      // 只有当markers数组不为空时才添加标记和聚焦
      if (markers.length > 0) {
        console.log(markers, '最终的打点数据')

        fdapi.marker.add(markers)
        fdapi.marker.focus(markers[0].id, 1200, 0)
        console.log('标记点添加成功，总数:', markers.length)
      } else {
        console.log('没有有效的位置数据可添加标记')
      }
    } catch (error) {
      console.error('添加标记点失败:', error)
    }
  }
}
// 处理打点
const handleCk = data => {
  tagDataList.value = data
  console.log('handlePeople 接收到的数据:', data)

  // 这里可以处理接收到的数据，例如在地图上标记位置等
  if (data) {
    // 处理位置数据
    const locations = data.locationData || data || ''
    const tagData = data.tagData || {}
    console.log('handlePeople locations:', locations)
    console.log('handlePeople locations 类型:', typeof locations)
    console.log('handlePeople locations 是否为数组:', Array.isArray(locations))

    // 验证 locations 是否为数组
    if (!Array.isArray(locations)) {
      console.error('handlePeople locations 不是数组:', locations)
      return
    }

    console.log('handlePeople locations 数组长度:', locations.length)

    // 可以在地图上标记这些位置
    try {
      // 先清除现有标记
      fdapi.marker.clear()

      // 遍历位置数据并添加标记
      let markers = []
      locations.forEach((location, index) => {
        console.log(`handlePeople location ${index}:`, location)

        // 检查多种可能的坐标字段名
        const xCoord = location.xcoordinate || location.longitude || location.lng || location.jd
        const yCoord = location.ycoordinate || location.latitude || location.lat || location.wd

        console.log(`handlePeople location ${index} 坐标:`, { xCoord, yCoord })

        if (xCoord && yCoord) {
          // 确保有ID，如果没有则生成一个唯一ID
          const markerId = 'CK_' + (location.id || location.teamId || `${index}_${Date.now()}`)

          // 创建标记配置
          const marker = {
            id: markerId,
            groupId: 'ckLocations',
            coordinate: [parseFloat(xCoord), parseFloat(yCoord), 0],
            coordinateType: 1, // 经纬度坐标系
            anchors: [-15, 30],
            imageSize: [30, 30],
            hoverImageSize: [30, 30],
            range: [100, 10000000],
            viewHeightRange: [100, 10000000],
            rangeRatio: 0.01,
            imagePath: '@path:screenfile/zhtb/phq.png',
            hoverImagePath: '@path:screenfile/zhtb/phq.png',
            fixedSize: true,
            text: location.name || `仓库${index + 1}`,
            useTextAnimation: true,
            textRange: [1, 1000000000],
            textOffset: [0, 0],
            fontSize: 12,
            fontColor: window.Color ? window.Color.White : '#FFFFFF',
            fontOutlineColor: window.Color ? window.Color.Black : '#000000',
            showLine: true,
            lineSize: [2, 100],
            lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
            displayMode: 4,
            autoHeight: true
          }
          console.log(`handlePeople marker ${index}:`, marker)

          markers.push(marker)
        } else {
          console.log(`handlePeople location ${index} 跳过，缺少坐标:`, { xCoord, yCoord })
        }
      })

      console.log(`handlePeople 总共创建了 ${markers.length} 个标记点`)

      // 只有当markers数组不为空时才添加标记和聚焦
      if (markers.length > 0) {
        console.log('handlePeople 最终的打点数据:', markers)

        try {
          fdapi.marker.add(markers)
          console.log('handlePeople fdapi.marker.add 调用成功')

          // 这个判断是智能感知的泵站运行统计独有的，其他地方都走else
          if (tagData.indexdx !== undefined) {
            fdapi.marker.focus(markers[tagData.indexdx].id, 1200, 0)
          } else {
            fdapi.marker.focus(markers[0].id, 1200, 0)
          }
          console.log('handlePeople 标记点添加成功，总数:', markers.length)
        } catch (markerError) {
          console.error('handlePeople 添加标记点到地图失败:', markerError)
        }
      } else {
        console.log('handlePeople 没有有效的位置数据可添加标记')
      }
    } catch (error) {
      console.error('添加标记点失败:', error)
    }
  }
}

// 处理视频打点
const handleVd = data => {
  // 这里可以处理接收到的数据，例如在地图上标记位置等
  if (data) {
    // 处理位置数据
    const locations = data.locationData || data || ''
    console.log('handlePeople locations:', locations)

    // 验证 locations 是否为数组
    if (!Array.isArray(locations)) {
      console.error('handlePeople locations 不是数组:', locations)
      return
    }

    console.log('handlePeople locations 数组长度:', locations.length)

    // 可以在地图上标记这些位置
    try {
      // 先清除现有标记
      fdapi.marker.clear()

      // 遍历位置数据并添加标记
      let markers = []
      locations.forEach((location, index) => {
        // 检查多种可能的坐标字段名
        const xCoord = location.jd || location.xcoordinate
        const yCoord = location.wd || location.ycoordinate

        console.log(`handlePeople location ${index} 坐标:`, { xCoord, yCoord })

        if (xCoord && yCoord) {
          // 确保有ID，如果没有则生成一个唯一ID
          const markerId = 'VD_' + (location.id || `${index}_${Date.now()}`)

          // 创建标记配置
          const marker = {
            id: markerId,
            groupId: 'vdLocations',
            coordinate: [parseFloat(xCoord), parseFloat(yCoord), 0],
            coordinateType: 1, // 经纬度坐标系
            anchors: [-15, 30],
            imageSize: [30, 30],
            hoverImageSize: [30, 30],
            range: [100, 10000000],
            viewHeightRange: [100, 10000000],
            rangeRatio: 0.01,
            imagePath: '@path:screenfile/zhtb/sxt.png',
            hoverImagePath: '@path:screenfile/zhtb/sxt.png',
            fixedSize: true,
            text: location.sxtmc || `视频${index + 1}`,
            useTextAnimation: true,
            textRange: [1, 1000000000],
            textOffset: [0, 0],
            fontSize: 12,
            fontColor: window.Color ? window.Color.White : '#FFFFFF',
            fontOutlineColor: window.Color ? window.Color.Black : '#000000',
            showLine: true,
            lineSize: [2, 100],
            lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
            displayMode: 4,
            autoHeight: true
          }
          console.log(`handlePeople marker ${index}:`, marker)

          markers.push(marker)
        } else {
          console.log(`handlePeople location ${index} 跳过，缺少坐标:`, { xCoord, yCoord })
        }
      })

      console.log(`handlePeople 总共创建了 ${markers.length} 个标记点`)

      // 只有当markers数组不为空时才添加标记和聚焦
      if (markers.length > 0) {
        console.log('handlePeople 最终的打点数据:', markers)

        try {
          fdapi.marker.add(markers)
          console.log('handlePeople fdapi.marker.add 调用成功')
          console.log('handlePeople 标记点添加成功，总数:', markers.length)
        } catch (markerError) {
          console.error('handlePeople 添加标记点到地图失败:', markerError)
        }
      } else {
        console.log('handlePeople 没有有效的位置数据可添加标记')
      }
    } catch (error) {
      console.error('添加标记点失败:', error)
    }
  }
}
// 处理防汛人员打点
const handlePeople = data => {
  tagDataList.value = data
  console.log('handlePeople 接收到的数据:', data)

  // 这里可以处理接收到的数据，例如在地图上标记位置等
  if (data) {
    // 处理位置数据
    const locations = data.locationData || data || ''
    const tagData = data.tagData || {}
    console.log('handlePeople locations:', locations)
    console.log('handlePeople locations 类型:', typeof locations)
    console.log('handlePeople locations 是否为数组:', Array.isArray(locations))

    // 验证 locations 是否为数组
    if (!Array.isArray(locations)) {
      console.error('handlePeople locations 不是数组:', locations)
      return
    }

    console.log('handlePeople locations 数组长度:', locations.length)

    // 可以在地图上标记这些位置
    try {
      // 先清除现有标记
      fdapi.marker.clear()

      // 遍历位置数据并添加标记
      let markers = []
      locations.forEach((location, index) => {
        console.log(`handlePeople location ${index}:`, location)

        // 检查多种可能的坐标字段名
        const xCoord = location.xcoordinate || location.longitude || location.lng || location.jd
        const yCoord = location.ycoordinate || location.latitude || location.lat || location.wd

        console.log(`handlePeople location ${index} 坐标:`, { xCoord, yCoord })

        if (xCoord && yCoord) {
          // 确保有ID，如果没有则生成一个唯一ID
          const markerId = 'FXEY_' + (location.id || location.teamId || `${index}_${Date.now()}`)

          // 创建标记配置
          const marker = {
            id: markerId,
            groupId: 'peopleLocations',
            coordinate: [parseFloat(xCoord), parseFloat(yCoord), 0],
            coordinateType: 1, // 经纬度坐标系
            anchors: [-15, 30],
            imageSize: [30, 30],
            hoverImageSize: [30, 30],
            range: [100, 10000000],
            viewHeightRange: [100, 10000000],
            rangeRatio: 0.01,
            imagePath: '@path:screenfile/zhtb/rxxc.png',
            hoverImagePath: '@path:screenfile/zhtb/rxxc.png',
            fixedSize: true,
            text: location.teamName || location.leader || location.name || location.userName || `人员${index + 1}`,
            useTextAnimation: true,
            textRange: [1, 1000000000],
            textOffset: [0, 0],
            fontSize: 12,
            fontColor: window.Color ? window.Color.White : '#FFFFFF',
            fontOutlineColor: window.Color ? window.Color.Black : '#000000',
            showLine: true,
            lineSize: [2, 100],
            lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
            displayMode: 4,
            autoHeight: true
          }
          console.log(`handlePeople marker ${index}:`, marker)

          markers.push(marker)
        } else {
          console.log(`handlePeople location ${index} 跳过，缺少坐标:`, { xCoord, yCoord })
        }
      })

      console.log(`handlePeople 总共创建了 ${markers.length} 个标记点`)

      // 只有当markers数组不为空时才添加标记和聚焦
      if (markers.length > 0) {
        console.log('handlePeople 最终的打点数据:', markers)

        try {
          fdapi.marker.add(markers)
          console.log('handlePeople fdapi.marker.add 调用成功')

          // 这个判断是智能感知的泵站运行统计独有的，其他地方都走else
          if (tagData.indexdx !== undefined) {
            fdapi.marker.focus(markers[tagData.indexdx].id, 1200, 0)
          } else {
            fdapi.marker.focus(markers[0].id, 1200, 0)
          }
          console.log('handlePeople 标记点添加成功，总数:', markers.length)
        } catch (markerError) {
          console.error('handlePeople 添加标记点到地图失败:', markerError)
        }
      } else {
        console.log('handlePeople 没有有效的位置数据可添加标记')
      }
    } catch (error) {
      console.error('添加标记点失败:', error)
    }
  }
}
// 处理场景分类-设备信息打点
const handleScene = data => {
  tagDataList.value = data
  console.log('rootMap接收到的数据:', data)
  // 这里可以处理接收到的数据，例如在地图上标记位置等
  if (data) {
    // 处理位置数据
    const locations = data.locationData || data || ''
    const tagData = data.tagData || ''
    console.log(locations, 'locations')

    // 验证 locations 是否为数组
    if (!Array.isArray(locations)) {
      console.error('locations 不是数组:', locations)
      return
    }

    // 可以在地图上标记这些位置
    try {
      // 先清除现有标记
      fdapi.marker.clear()

      // 遍历位置数据并添加标记
      let markers = []
      locations.forEach((location, index) => {
        console.log(location, 'location111111111111111111111')
        if (location.xcoordinate && location.ycoordinate) {
          // 确保有ID，如果没有则生成一个唯一ID
          const markerId = location.yldId || location.id || `scene_marker_${index}_${Date.now()}`

          // 创建标记配置
          const marker = {
            id: markerId,
            groupId: 'senceLocations',
            coordinate: [parseFloat(location.xcoordinate), parseFloat(location.ycoordinate), 0],
            coordinateType: 1, // 经纬度坐标系
            anchors: [-15, 30],
            imageSize: [30, 30],
            hoverImageSize: [30, 30],
            range: [100, 10000000],
            viewHeightRange: [100, 10000000],
            rangeRatio: 0.01,
            imagePath: tagData.icon2 || '@path:screenfile/zhtb/cl.png',
            hoverImagePath: tagData.icon2 || '@path:screenfile/zhtb/cl.png',
            fixedSize: true,
            text: location.sbname || location.name,
            useTextAnimation: true,
            textRange: [1, 1000000000],
            textOffset: [0, 0],
            fontSize: 12,
            fontColor: window.Color ? window.Color.White : '#FFFFFF',
            fontOutlineColor: window.Color ? window.Color.Black : '#000000',
            showLine: true,
            lineSize: [2, 100],
            lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
            displayMode: 4,
            autoHeight: true
          }
          console.log(marker, 'marker')

          markers.push(marker)
        }
      })

      // 只有当markers数组不为空时才添加标记和聚焦
      if (markers.length > 0) {
        console.log(markers, '最终的打点数据')

        fdapi.marker.add(markers)
        // 这个判断是智能感知的泵站运行统计独有的，其他地方都走else
        if (tagData.indexdx !== undefined) {
          fdapi.marker.focus(markers[tagData.indexdx].id, 1200, 0)
        } else {
          fdapi.marker.focus(markers[0].id, 1200, 0)
        }
        console.log('标记点添加成功，总数:', markers.length)
      } else {
        console.log('没有有效的位置数据可添加标记')
      }
    } catch (error) {
      console.error('添加标记点失败:', error)
    }
  }
}

// 降雨日历打点
const handleDate = data => {
  tagDataList.value = data
  console.log('降雨日历打点数据', data)

  if (data) {
    const locations = data.locationData
    console.log(locations, 'locations')

    // 验证 locations 是否为数组
    if (!Array.isArray(locations)) {
      console.error('locations 不是数组:', locations)
      return
    }

    try {
      fdapi.marker.clear()
      let markers = []
      locations.forEach((location, index) => {
        console.log(location, 'location111111111111111111111')

        if (location.xcoordinate && location.ycoordinate) {
          const markerId = location.id || `marker_${index}`

          const marker = {
            id: markerId,
            groupId: 'dateLocations',
            coordinate: [parseFloat(location.xcoordinate), parseFloat(location.ycoordinate), 0],
            coordinateType: 1,
            anchors: [-15, 30],
            imageSize: [30, 30],
            hoverImageSize: [30, 30],
            range: [100, 10000000],
            viewHeightRange: [100, 10000000],
            rangeRatio: 0.01,
            imagePath: '@path:screenfile/zhtb/llj.png',
            hoverImagePath: '@path:screenfile/zhtb/llj.png',
            fixedSize: true,
            text: location.name,
            useTextAnimation: true,
            textRange: [1, 1000000000],
            textOffset: [0, 0],
            fontSize: 24,
            fontColor: window.Color ? window.Color.White : '#FFFFFF',
            fontOutlineColor: window.Color ? window.Color.Black : '#000000',
            showLine: true,
            lineSize: [2, 100],
            lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
            displayMode: 4,
            autoHeight: true,
            // 弹窗URL携带markerId（供弹窗识别自己对应的 data）
            popupURL: `@path:popup-template.html?name=${location.name}+&.html&waterLevel=${location.waterLevel}+&.html&waterDepth=${location.waterDepth}+&.html`

            // autoHidePopupWindow: false
          }

          markers.push(marker)
        }
      })

      if (markers.length > 0) {
        console.log(markers, '最终的打点数据')

        fdapi.marker.add(markers, () => {
          console.log('标记点添加成功，总数:', markers.length)
          fdapi.marker.focus(markers[0].id, 1200, 0)
          // 直接打开所有弹窗，弹窗会自行读取数据
          fdapi.marker.showAllPopupWindow(() => {
            console.log('所有弹窗已打开，弹窗将自动加载动态内容')
          })
        })
      } else {
        console.log('没有有效的位置数据可添加标记')
      }
    } catch (error) {
      console.error('添加标记点失败:', error)
    }
  }
}
// 处理子组件传来的标签数据
const handleTagData = data => {
  tagDataList.value = data
  console.log('rootMap接收到的数据:', data)
  // 这里可以处理接收到的数据，例如在地图上标记位置等
  if (data) {
    // 处理位置数据
    const locations = data.locationData
    const tagData = data.tagData || []
    console.log(locations, 'locations')

    // 验证 locations 是否为数组
    if (!Array.isArray(locations)) {
      console.error('locations 不是数组:', locations)
      return
    }

    // 可以在地图上标记这些位置
    try {
      // 先清除现有标记
      fdapi.marker.clear()

      // 遍历位置数据并添加标记
      let markers = []
      locations.forEach((location, index) => {
        console.log(location, 'location111111111111111111111')
        if (location.xcoordinate && location.ycoordinate) {
          // 确保有ID，如果没有则生成一个唯一ID
          const markerId = location.id || `tag_marker_${index}_${Date.now()}`

          // 创建标记配置
          const marker = {
            id: markerId,
            groupId: 'tagLocations',
            coordinate: [parseFloat(location.xcoordinate), parseFloat(location.ycoordinate), 0],
            coordinateType: 1, // 经纬度坐标系
            anchors: [-15, 30],
            imageSize: [30, 30],
            hoverImageSize: [30, 30],
            range: [100, 10000000],
            viewHeightRange: [100, 10000000],
            rangeRatio: 0.01,
            imagePath: tagData.icon2 || '@path:screenfile/zhtb/cl.png',
            hoverImagePath: tagData.icon2 || '@path:screenfile/zhtb/cl.png',
            fixedSize: true,
            text: location.sbname,
            userData: location.taiyuanFlag + '?' + location.cbdId, // 传入marker数据, taiyuanFlag手动定义，智能感知里面的应急物资中使用
            useTextAnimation: true,
            textRange: [1, 1000000000],
            textOffset: [0, 0],
            fontSize: 24,
            fontColor: window.Color ? window.Color.White : '#FFFFFF',
            fontOutlineColor: window.Color ? window.Color.Black : '#000000',
            showLine: true,
            lineSize: [2, 100],
            lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
            displayMode: 4,
            autoHeight: true
          }
          console.log(marker, 'marker')

          markers.push(marker)
        }
      })

      // 只有当markers数组不为空时才添加标记和聚焦
      if (markers.length > 0) {
        console.log(markers, '最终的打点数据')

        fdapi.marker.add(markers)
        fdapi.marker.focus(markers[0].id, 1200, 0)
        console.log('标记点添加成功，总数:', markers.length)
      } else {
        console.log('没有有效的位置数据可添加标记')
      }
    } catch (error) {
      console.error('添加标记点失败:', error)
    }
  }
}

// 单个点位数据
const singleMarkerData = ref(null)

// 单个地图打点
const handleClickTable = data => {
  singleMarkerData.value = data

  // 检查数据是否存在
  if (!data || !data.tableData) {
    console.error('无效的表格数据')
    return
  }

  const tableData = data.tableData
  console.log(JSON.stringify(tableData), 'tableData', '单个地图打点data')

  // 检查是否有坐标数据
  if (!tableData.coordinate) {
    console.error('坐标数据不存在')
    return
  }

  try {
    // 分割坐标字符串为经纬度数组
    const coordinates = tableData.coordinate.split(',').map(coord => parseFloat(coord))
    console.log(coordinates, 'coordinates')

    // 验证坐标数据有效性
    if (coordinates.length < 2 || isNaN(coordinates[0]) || isNaN(coordinates[1])) {
      console.error('无效的坐标数据:', tableData.coordinate)
      return
    }

    // 清除现有标记
    fdapi.marker.clear()

    // 确保有ID，如果没有则生成一个唯一ID
    const markerId = `TAB-${Date.now()}`

    // 创建新的标记
    const singleMarker = {
      id: markerId,
      groupId: 'singleMarker',
      coordinate: [coordinates[0], coordinates[1]], // 使用分割后的经纬度
      coordinateType: 1, // 经纬度坐标系
      anchors: [-15, 30],
      imageSize: [30, 30],
      hoverImageSize: [30, 30],
      range: [100, 10000000],
      viewHeightRange: [100, 10000000],
      rangeRatio: 0.01,
      imagePath: '@path:screenfile/zhtb/dw.png',
      hoverImagePath: '@path:screenfile/zhtb/dw.png',
      fixedSize: true,
      text:
        tableData.eventGridName ||
        tableData.station ||
        tableData.device_name ||
        tableData.name ||
        tableData.wzname ||
        tableData.bcname ||
        '',
      useTextAnimation: true,
      textRange: [1, 1000000000],
      textOffset: [0, 0],
      fontSize: 24,
      fontColor: window.Color ? window.Color.White : '#FFFFFF',
      fontOutlineColor: window.Color ? window.Color.Black : '#000000',
      showLine: true,
      lineSize: [2, 100],
      lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
      displayMode: 4,
      autoHeight: true
    }
    console.log(singleMarker, 'singleMarker')
    fdapi.marker.add([singleMarker])
    fdapi.marker.focus(singleMarker.id, 1000, 0)
    console.log('singleMarker打点成功')
  } catch (error) {
    console.error('添加单个标记点失败:', error)
  }
}

// 视频打点
const handleVideoClicked = data => {
  singleMarkerData.value = data

  // 检查数据是否存在
  if (!data || !data.tableData) {
    console.error('无效的视频数据')
    return
  }

  const tableData = data.tableData
  console.log(data, '单个地图打点data')

  try {
    // 清除现有标记
    fdapi.marker.clear()

    // 创建新的标记
    const videoMarker = {
      id: tableData.id,
      groupId: 'singleMarker',
      coordinate: [tableData.jd, tableData.wd, 0], // 使用分割后的经纬度
      coordinateType: 1, // 经纬度坐标系
      anchors: [-15, 30],
      imageSize: [30, 30],
      hoverImageSize: [30, 30],
      range: [100, 10000000],
      viewHeightRange: [1000, 10000000], // 增加视角高度下限，从200改为1000
      rangeRatio: 0.01,
      imagePath: '@path:screenfile/zhtb/bz.png',
      hoverImagePath: '@path:screenfile/zhtb/bz.png',
      fixedSize: true,
      text: tableData.cameraManufacturer || '',
      useTextAnimation: true,
      textRange: [1, 1000000000],
      textOffset: [0, 0],
      fontSize: 24,
      fontColor: window.Color ? window.Color.White : '#FFFFFF',
      fontOutlineColor: window.Color ? window.Color.Black : '#000000',
      showLine: true,
      lineSize: [2, 100],
      lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
      displayMode: 4,
      autoHeight: true
    }
    fdapi.marker.add([videoMarker])
    fdapi.marker.focus(videoMarker.id, 1000, 0) // 增加飞入高度，从100改为1000
    console.log('videoMarker打点成功')
  } catch (error) {
    console.error('添加单个标记点失败:', error)
  }
}

const deviceData = ref()
const getDeviceData = async id => {
  if (!id) {
    console.error('设备ID不存在')
    return
  }

  try {
    const result = await getDeviceDetails1(id)
    // console.log('设备详情数据1111:', result)

    if (result && result.code === 200) {
      deviceData.value = result.data
      console.log('deviceData.value', deviceData.value)
    } else {
      console.error('获取设备数据失败或数据为空,调用防汛车辆数据')
      deviceData.value = null
    }
  } catch (error) {
    console.error('获取设备详情失败:', error)
    deviceData.value = null
  }
}

const getSuppliesData = async id => {
  if (!id) {
    console.error('设备ID不存在')
    return
  }

  try {
    const result = await getSuppliesDetails(id)
    // console.log('设备详情数据1111:', result)

    if (result && result.code === 200) {
      deviceData.value = result.data
      console.log('deviceData.value', deviceData.value)
    } else {
      console.error('获取应急物资数据失败或数据为空,调用其他数据')
      deviceData.value = null
    }
  } catch (error) {
    console.error('获取设备详情失败:', error)
    deviceData.value = null
  }
}
//  防汛车辆
// const getCarDataDeails = async id => {
//   if (!id) {
//     console.error('设备ID不存在')
//     return
//   }

//   try {
//     const result = await getCarData(id)
//     // console.log('设备详情数据1111:', result)

//     if (result && result.code === 200) {
//       deviceData.value = result.rows[0]
//       console.log('deviceData.value', deviceData.value)
//     } else {
//       console.error('获取防汛数据失败或数据为空,调用应急物资数据')
//       deviceData.value = null
//     }
//   } catch (error) {
//     console.error('获取设备详情失败:', error)
//     deviceData.value = null
//   }
// }
// 处理地图点击事件
const clickX = ref(0)
const clickY = ref(0)

const yingjiwuziRootGETTable = () => {
  yingjiwuziRootTable(cbdIdValue.value, yjwzPageNum.value, yjwzPageSize.value).then(res => {
    console.log(res, 'RootTable')
    yjwzdialogVisible.value = true
    if (res.code === 200) {
      yjwztableData.value = res.rows
      yjwzTotal.value = res.total
    }
  })
}

const deviceDetails = ref(null)
// 获取设备详情 event.id
const getDeviceDetails = async id => {
  try {
    const result = await getdeviceInfo(id)
    console.log('设通过名称查询设备信息备详情数据:', result)
    if (result && result.code === 200) {
      deviceDetails.value = result.rows[0]
      if (deviceDetails.value) {
        gethsfq(deviceDetails.value.sbname)
        getwaterLevelData(deviceDetails.value.sncode)
      }

      console.log('设通过名称查询设备信息备详情数据成功', deviceDetails.value)
    } else {
      message.error('获取设备详情失败')
    }
  } catch (error) {
    console.error('获取设备详情失败:', error)
    deviceData.value = null
  }
}
const waterLevelData = ref(null)
const qzxxData = ref([])
// 获取设备水位数据 deviceDetails.sncode
const getwaterLevelData = async id => {
  try {
    const result = await getwaterLevel(id)
    console.log('获取设备水位数据:', result)
    if (result && result.code === 200) {
      waterLevelData.value = result.rows
      console.log('获取设备水位数据成功', waterLevelData.value)
      // 数据加载完成后更新图表
      setTimeout(() => {
        updateChart1WithWaterLevel()
      }, 100)
    } else {
      console.error('获取设备水位失败')
    }
  } catch (error) {
    console.error('获取设备水位失败:', error)
  }
}
// 获取权责信息event.id
const getQzxxData = async id => {
  try {
    const result = await getQzxx(id)
    console.log('获取权责信息:', result)
    if (result && result.code === 200) {
      qzxxData.value = result.rows
      console.log('获取权责信息成功', qzxxData.value)
    } else {
      console.error('获取权责信息失败')
    }
  } catch (error) {
    console.error('获取权责信息失败')
  }
}

const videoData = ref(null)
// 当前选中的视频索引
const selectedVideoIndex = ref(null)
// 当前选中的视频对象
const selectedVideo = ref(null)

// 选择视频的方法
const selectVideo = index => {
  selectedVideoIndex.value = index
  selectedVideo.value = videoData.value[index]
  console.log('选中视频:', selectedVideo.value)
}

// 获取视频信息-event.id
const getVideoData = async id => {
  try {
    const result = await getVideoInfo()
    console.log('获取视频信息:', result)
    if (result && result.code === 200) {
      videoData.value = result.rows
      console.log('获取视频信息成功', videoData.value)
      // 默认选中第一个视频
      if (videoData.value && videoData.value.length > 0) {
        selectVideo(0)
      }
    } else {
      console.error('获取视频信息失败')
    }
  } catch (error) {
    console.error('获取视频信息失败')
  }
}
const fxryData = ref(null)
// 防汛人员弹窗
const getFxryDataDeails = async id => {
  if (!id) {
    console.error('防汛人员ID不存在')
    return
  }
  try {
    const result = await getFxryData(id)
    // console.log('设备详情数据1111:', result)

    if (result && result.code === 200) {
      fxryData.value = result.data
      console.log('fxryData.value', fxryData.value)
    }
  } catch (error) {
    console.error('防汛人员数据失败:', error)
    fxryData.value = null
  }
}
// 连接数字孪生配置
const options = ref({
  domId: 'player', // 修改为新的ID
  resset: true,
  apiOptions: {
    onReady: function () {
      fdapi.settings.setMainUIVisibility(false)
      init2DWater()
    },
    onEvent: async function (event) {
      console.log('地图点击事件:', event)
      if (event.eventtype === 'LeftMouseButtonClick' && event.Type === 'marker') {
        // 这里处理应急物资的逻辑
        // 取marker里面的userDATA数据
        if (event.UserData) {
          // 确定应急物资后，需要展示弹窗显示当前仓库下的所有应急物资数据

          const yjwzSplit = event.UserData.split('?')
          if (yjwzSplit[0] === 'taiyuanFlag' && yjwzSplit[1]) {
            cbdIdValue.value = yjwzSplit[1]
            yingjiwuziRootGETTable()
            return
          }
        }

        // 视频
        if (event.Id.startsWith('VD_')) {
          await getvideoDeatilsData(event.Id.replace('VD_', ''))
          // await getvideoDeatilsData(4756)
          vdPreviewVisible.value = true
          console.log('包含VD_')
          return
        }

        // 应急物资
        if (event.Id.startsWith('CK_')) {
          await getCkDataDeails(event.Id.replace('CK_', ''))

          ckPreviewVisible.value = true
          console.log('包含CK_')
          return
        }

        // 防汛人员
        if (event.Id.startsWith('FXEY_')) {
          await getFxryDataDeails(event.Id.replace('FXEY_', ''))
          peoplePreviewVisible.value = true
          console.log('包含FXEY_')
          return
        }

        if (event.Id.startsWith('YLD')) {
          getDeviceDetails(event.Id)
          getQzxxData(event.Id)
          getVideoData(event.Id)

          setTimeout(() => {
            deviceDialogVisible.value = true
          }, 1000)
          setTimeout(() => {
            initCharts()
          }, 1200)
          console.log('包含YLD')
        }
        // 防汛车辆弹窗
        if (event.Id.startsWith('FXCL_')) {
          await getYJBCDataDeails(event.Id.replace('FXCL_', ''))
          carPreviewVisible.value = true
          console.log('包含FXCL_')
          return
        }

        // 应急泵车弹窗
        if (event.Id.startsWith('YJBC_')) {
          await getCarDataDeails(event.Id.replace('YJBC_', ''))
          yjbcPreviewVisible.value = true
          console.log('YJBC_')
          return
        }
        // 物资弹窗
        if (event.Id.startsWith('WZ_')) {
          await getWzDataDetails(event.Id.replace('WZ_', ''))
          wzPreviewVisible.value = true
          console.log('WZ_')
          return
        }
        // 表格弹窗
        if (event.Id.startsWith('TAB')) {
          tablePreviewVisible.value = true
        }
        // 表格弹窗
        if (event.Id.startsWith('DDZL_')) {
          console.log(event, 'event')
          diaoduzhilingObject.value = diaoduzhiliangData.value[event.Id[event.Id.length - 1] - 0]
          console.log(diaoduzhilingObject.value, 'diaoduzhilingObject')
          diaoduzhilingtongjiVisible.value = true
          // tablePreviewVisible.value = true
        }

        // 原有的marker点击处理逻辑
        // if (!singleMarkerData.value) {
        //   getDeviceData(event.Id)
        //   getCarDataDeails(event.Id)
        //   getSuppliesData(event.id)
        // }
        // console.log('event.Id:', event.Id, singleMarkerData.value, deviceData.value)
        // dialogVisible.value = true
      }
    }
    // resset: false
    // onReady: function () {
    //   console.info('此时可以调API了')
    //   fdapi.marker.clear()
    //   //支持经纬度坐标和普通投影坐标两种类型
    //   let o1 = {
    //     id: 'm1',
    //     groupId: 'markerAdd',
    //     coordinate: [112.53279714, 37.70480413, 0], //坐标位置
    //     coordinateType: 1, //默认0是投影坐标系，也可以设置为经纬度空间坐标系值为1
    //     anchors: [-25, 50], //锚点，设置Marker的整体偏移，取值规则和imageSize设置的宽高有关，图片的左上角会对准标注点的坐标位置。示例设置规则：x=-imageSize.width/2，y=imageSize.height
    //     imageSize: [100, 100], //图片的尺寸
    //     hoverImageSize: [100, 100], //鼠标悬停时显示的图片尺寸
    //     range: [1, 100000], //可视范围
    //     viewHeightRange: [100, 10000], // 可见高度范围
    //     rangeRatio: 0.01, //可见高度范围的调整系数
    //     imagePath: '@path:screenfile/zhtb/bz.png', //显示图片路径
    //     hoverImagePath: '@path:screenfile/zhtb/cl.png', // 鼠标悬停时显示的图片路径
    //     fixedSize: true, //图片固定尺寸，取值范围：false 自适应，近大远小，true 固定尺寸，默认值：false
    //     text: '北京银行', //显示的文字
    //     useTextAnimation: false, //关闭文字展开动画效果 打开会影响效率
    //     textRange: [1, 1000], //文本可视范围[近裁距离, 远裁距离]
    //     textOffset: [0, 0], // 文本偏移
    //     textBackgroundColor: Color.SpringGreen, //文本背景颜色
    //     fontSize: 24, //字体大小
    //     fontOutlineSize: 1, //字体轮廓线大小
    //     fontColor: Color.White, //字体颜色
    //     fontOutlineColor: Color.Black, //字体轮廓线颜色
    //     showLine: true, //标注点下方是否显示垂直牵引线
    //     lineSize: [2, 100], //垂直牵引线宽度和高度[width, height]
    //     lineColor: Color.SpringGreen, //垂直牵引线颜色
    //     lineOffset: [0, 0], //垂直牵引线偏移
    //     autoHidePopupWindow: true, //失去焦点后是否自动关闭弹出窗口
    //     autoHeight: false, // 自动判断下方是否有物体
    //     displayMode: 4, //智能显示模式  开发过程中请根据业务需求判断使用四种显示模式
    //     clusterByImage: true, // 聚合时是否根据图片路径分类，即当多个marker的imagePath路径参数相同时按路径对marker分类聚合
    //     priority: 0, //避让优先级
    //     occlusionCull: false //是否参与遮挡剔除
    //   }
    //   fdapi.marker.add([o1])
    //   fdapi.marker.focus(o1.id, 100, 0)
    // }
  }
})
const loadLinkGeojson = async () => {
  // 添加前先清除保证id唯一
  //fdapi.geoJSONLayer.clear()

  // 简单渲染器
  const simpleRenderer = {
    // 渲染器类型
    rendererType: RendererType.SimpleRenderer,
    // 默认符号化配置
    defaultSymbol: {
      // 符号化类型枚举：0 simple-marker圆形点填充  1 simple-line线填充  2 simple-fill面填充 3 polygon3d填充
      symbolType: 1,
      // 填充颜色
      color: [1, 0, 0, 1],
      // 默认轮廓线
      outline: {
        // 线宽
        width: 8
      }
    }
  }

  // 用简单渲染器添加GeoJSONLayer
  await fdapi.geoJSONLayer.add({
    id: 'layer2',
    visible: true, // 加载后是否显示
    rotation: [0, 0, 0], // 图层旋转
    offset: [0, 0, 0], // 基于原始位置的偏移量
    needProject: false, // 开启投影转换
    collision: true, // 开启碰撞
    onTerrain: true, // 是否贴地
    url: import.meta.env.VITE_DTS_GW,
    renderer: simpleRenderer
  })
  // fdapi.geoJSONLayer.focus("layer2", 100);
}

const subcatchGeojson = async () => {
  // 添加前先清除保证id唯一
  //fdapi.geoJSONLayer.clear();

  // 简单渲染器
  const simpleRenderer = {
    // 渲染器类型
    rendererType: RendererType.SimpleRenderer,
    // 默认符号化配置
    defaultSymbol: {
      // 符号化类型枚举：0 simple-marker圆形点填充  1 simple-line线填充  2 simple-fill面填充 3 polygon3d填充
      symbolType: 3,
      // 默认高度
      height: 20,
      // 默认填充颜色
      color: [0, 0, 0, 0],
      // 默认轮廓线
      outline: {
        // 线宽
        width: 10,
        // 颜色
        color: [0, 0, 1, 1]
      }
    }
  }

  // 用简单渲染器添加GeoJSONLayer
  fdapi.geoJSONLayer.add({
    id: 'subcatchLayer',
    visible: true, // 加载后是否显示
    rotation: [0, 0, 0], // 图层旋转
    offset: [0, 0, 0], // 基于原始位置的偏移量
    needProject: false, // 开启投影转换
    textMarkerField: 'TextString',
    textRange: [0, 1000000000], // 文字标注可见范围
    onTerrain: true, // 是否贴地
    collision: true, // 开启碰撞
    url: import.meta.env.VITE_DTS_PSFQ, // 这里切换成自己的路径
    renderer: simpleRenderer
  })

  // setTimeout(function () {
  //   fdapi.geoJSONLayer.focus("'subcatchLayer", 100);
  // }, 2000);
}

const api = ref(null)
// 太原数字孪生内网地址
const host = ref(import.meta.env.VITE_DTS_URL)

onMounted(async () => {
  // getZdcj()
  // getDeviceDetails()
  // getVideoData()
  // 获取权责信息数据
  // getQzxxData()
  // 获取场景类型列表
  getZdcjTypeList()
  // 获取人员定位数据
  getRydwData()

  // 获取视频类型数据
  getVideoTypeData()

  // 获取其他视频数据（初始化时使用默认参数）
  getOtherVideoData({ pageSize: 100 })

  // 初始化图表
  // setTimeout(() => {
  //   initCharts()
  //   // 图表初始化完成后获取水位数据
  //   setTimeout(() => {
  //     getwaterLevelData()
  //   }, 200)
  // }, 100)

  // 添加键盘事件监听器
  document.addEventListener('keydown', handleKeydown)

  // fdapi.weather.disableRainSnow()

  // getDeviceData('507')
  // getDeviceData('SHB-25825165')
  // dialogVisible.value = true
  // 修改这里，接入地图配置
  try {
    // 确保先引入了ac.min.js
    if (typeof acapi !== 'undefined') {
      // 创建数字孪生平台实例
      console.log('加载飞渡')
      api.value = await new DigitalTwinPlayer(host.value, options.value)

      console.log('数字孪生平台初始化成功')
    } else {
      console.error('ac.min.js未正确加载，请检查引入路径')
    }
  } catch (error) {
    console.error('数字孪生平台初始化失败:', error)
  }
  setTimeout(() => {
    if (mapRef.value) {
      loadLinkGeojson()
      subcatchGeojson()
      console.log('管网加载-排水分区！！！！！！')
    }
  }, 3000)

  // 添加全局点击事件监听器
  // document.addEventListener('click', handleGlobalClick)
})

// 组件卸载时移除事件监听器
onUnmounted(() => {
  fdapi.weather.disableRainSnow()
  fdapi.marker.clear()
  // 移除键盘事件监听器
  document.removeEventListener('keydown', handleKeydown)
  // document.removeEventListener('click', handleGlobalClick)
})

const handleSizeChangeyjwz = size => {
  yjwzPageSize.value = size
  yjwzPageNum.value = 1
  yingjiwuziRootGETTable()
}
const handleCurrentChangeyjwz = current => {
  yjwzPageNum.value = current
  yingjiwuziRootGETTable()
}
</script>
<style lang="scss" scoped>
:deep(.el-tabs__active-bar) {
}

:deep(.el-tabs__nav-wrap::after) {
  background: transparent !important;
}

.car-preview-overlay {
  border: 1px solid #4190d8;
  position: fixed;
  top: 20%;
  left: 0;
  width: 1000px;
  height: 800px;
  pointer-events: auto;
  background: rgba(26, 72, 123, 0.9693);
  display: flex;
  z-index: 10000;
  backdrop-filter: blur(5px);
  margin-left: 40%;
  background: rgba(26, 72, 123, 0.9);
  box-shadow: inset 0px 0px 57px 0px #0e4571;
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #4190d8;

  //opacity: 0.95;
  .car-dialog-header {
    background: linear-gradient(270deg, #2882bf 0%, rgba(38, 128, 190, 0.3862) 100%);
    padding: 10px 20px;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    width: 100.1%;
    align-items: center;
    font-weight: 600;
    background: linear-gradient(270deg, #2882bf 0%, rgba(38, 128, 190, 0.3862) 100%);
    border-radius: 0px 0px 0px 0px;

    .car-dialog-title {
      color: #ffffff;
      font-size: 38px;
      font-weight: bold;
      margin: 0;
      padding-left: 30px;
    }

    .car-dialog-close {
      color: #ffffff;
      font-size: 24px;
      cursor: pointer;
      width: 30px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      transition: all 0.3s ease;
    }
  }

  .car-preview-dialog {
    width: 100%;
    margin-top: 90px;

    .car-info {
      margin-top: 50px;
      position: relative;
      height: 50px;
      line-height: 50px;
      font-size: 40px;
      color: #fff;
      width: 90%;
      margin-left: 5%;
      font-size: 32px;
    }

    .car-vd {
      width: 90%;
      margin: 30px auto;
      height: 350px;
      max-height: 350px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;

      .video-player-container {
        width: 100%;
        height: 100%;
        max-height: 350px;
        background: #000;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

        .video-player {
          width: 100%;
          height: 100%;
          max-height: 350px;
          object-fit: contain;
        }
      }

      .no-video-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 350px;
        max-height: 350px;
        background: rgba(255, 255, 255, 0.05);
        border: 2px dashed rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        color: rgba(255, 255, 255, 0.6);

        .no-video-icon {
          font-size: 48px;
          margin-bottom: 16px;
          opacity: 0.5;
        }

        .no-video-text {
          font-size: 18px;
          font-weight: 500;
        }
      }
    }
  }
}

.yuntu-img {
  width: 2140px;
  height: 800px;
  background: url('@/assets/images/intellisensenew/yuntu.png') no-repeat center/100%;
}

.yunzhongkuaibao-img {
  width: 2140px;
  height: 800px;
  background: url('@/assets/images/intellisensenew/yuzhongkuaibao.png') no-repeat center/100%;
}

// :deep(.el-icon svg) {
//   width: 200px !important;
//   height: 200px !important;
// }

// :deep(.el-dialog__headerbtn .el-dialog__close) {
//   color: #ffffff !important;
//   font-size: 60px !important;
// }
.top-tools {
  position: absolute;
  top: 20px;
  left: -9%;
  z-index: 10000;
}

.progress-wrapper {
  position: absolute;
  //bottom: 50px;
  //left: 108%;
  //transform: translateX(-50%);
  //width: 80%;
  z-index: 10000;
}

.floating-progress {
  background-color: rgba(255, 255, 255, 0.8);
  padding: 10px 80px 20px 80px;
  //height: 50px !important;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 覆盖Element Plus默认样式，使进度条更加突出 */
.floating-progress :deep(.el-slider__runway) {
  background-color: rgba(0, 0, 0, 0.1);
}

.floating-progress :deep(.el-slider__bar) {
  background-color: #409eff;
}

.floating-progress :deep(.el-slider__button) {
  border-color: #409eff;
}

.floating-progress :deep(.el-slider__marks-text) {
  color: #606266;
}

.roow-div {
  position: relative;
  height: 100vh;

  // border: 2px solid red;
  .map-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // border: 1px solid yellow;
    z-index: 1; // 添加z-index，确保地图在底层
  }

  .screen-wrap {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // border: 1px solid yellow;
    pointer-events: none; // 这里保持none，让鼠标事件穿透到地图
    z-index: 2; // 添加z-index，确保UI元素在地图上层

    .map-popup-container {
      :deep(.el-dialog__headerbtn .el-dialog__close) {
        color: #ffffff !important;
        font-size: 60px !important;
      }
    }

    /* 确保弹窗和其他需要交互的元素可以接收鼠标事件 */
    .map-popup-container,
    .el-dialog,
    .el-dialog__wrapper,
    .el-dialog__header,
    .el-dialog__headerbtn,
    .el-dialog__close {
      pointer-events: auto !important;
      z-index: 10000;
    }

    .middle-content {
      position: absolute;
      bottom: 250px;
      right: 37%;
      width: 2110px;
      //height: 200px;
      pointer-events: auto;
    }

    /* 导航栏样式 */
    .nav-bar {
      position: absolute;
      top: 13%;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      border-radius: 8px;
      padding: 5px;
      pointer-events: auto; // 保持导航栏可点击
      z-index: 100;

      .nav-item {
        width: vw(200);
        height: vh(80);
        background-image: url('@/assets/images/home/<USER>');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        display: flex;
        justify-content: center;
        padding-top: vh(14);
        font-family: JiangChengXieHei;
        color: #fff;
        font-size: vh(24);
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .nav-item.active {
        background-image: url('@/assets/images/home/<USER>');
        color: #fff;
        font-weight: bold;
      }
    }

    /* 中间顶部背景图样式 */
    .middle-top {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 30%;
      height: vh(220);
      background-image: url('@/assets/images/middle-top.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      padding-top: vh(20);
      z-index: 90;

      .section-title {
        font-family: YouSheBiaoTiHei;
        font-size: vh(80);
        color: #ffffff;
        text-align: center;
        font-style: normal;
      }
    }

    /* 中间标签样式 */
    .middle-tabs {
      position: absolute;
      left: 32.5%;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      flex-direction: column;
      gap: vh(30);
      z-index: 100;
      pointer-events: auto;

      .tab-item {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 15px;
        background: linear-gradient(90deg, rgba(0, 123, 255, 0.8) 0%, rgba(0, 123, 255, 0.4) 100%);
        border-radius: 25px;
        border: 1px solid rgba(0, 162, 236, 0.6);
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 120px;
        backdrop-filter: blur(5px);
        pointer-events: auto;

        &:hover {
          background: linear-gradient(90deg, rgba(0, 162, 236, 0.9) 0%, rgba(0, 162, 236, 0.6) 100%);
          box-shadow: 0 4px 15px rgba(0, 162, 236, 0.4);
        }

        .tab-icon {
          width: 20px;
          height: 20px;
          margin-right: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }

        .tab-text {
          color: #ffffff;
          font-size: 14px;
          font-weight: 500;
          white-space: nowrap;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
          cursor: pointer;
          transition: all 0.3s ease;
        }
      }
    }

    /* 右侧面板样式 */
    .right-panel {
      position: absolute !important;
      top: 50% !important;
      left: calc(32.5% + 150px) !important;
      /* 在tab-item右侧 */
      transform: translateY(-50%) !important;
      width: 350px !important;
      max-height: 80vh !important;
      background: linear-gradient(180deg, rgba(0, 30, 60, 0.95) 0%, rgba(0, 20, 40, 0.95) 100%) !important;
      backdrop-filter: blur(10px) !important;
      border: 2px solid rgba(0, 255, 136, 0.3) !important;
      border-radius: 12px !important;
      z-index: 10001 !important;
      transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
      opacity: 0 !important;
      transform: translateY(-50%) translateX(-20px) !important;

      &.panel-visible {
        opacity: 1 !important;
        transform: translateY(-50%) translateX(0) !important;
      }

      .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        background: linear-gradient(90deg, rgba(0, 255, 136, 0.2) 0%, rgba(0, 123, 255, 0.2) 100%);
        border-bottom: 1px solid rgba(0, 255, 136, 0.3);
        border-radius: 10px 10px 0 0;

        .panel-title {
          color: #ffffff;
          font-size: 16px;
          font-weight: 600;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        .panel-close {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 50%;
          color: #ffffff;
          font-size: 20px;
          cursor: pointer;
          transition: all 0.3s ease;
          pointer-events: auto;

          &:hover {
            background: rgba(255, 0, 0, 0.3);
            transform: scale(1.1);
          }
        }
      }

      .panel-content {
        max-height: calc(80vh - 80px);
        overflow-y: auto;
        padding: 16px;

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(0, 255, 136, 0.5);
          border-radius: 3px;

          &:hover {
            background: rgba(0, 255, 136, 0.7);
          }
        }
      }

      .list-container {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .list-item {
        display: flex;
        align-items: center;
        padding: 12px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 6px;
        border: 1px solid rgba(0, 255, 136, 0.2);
        cursor: pointer;
        transition: all 0.3s ease;
        pointer-events: auto;

        &:hover {
          background: rgba(0, 255, 136, 0.1);
          border-color: rgba(0, 255, 136, 0.5);
        }

        .item-icon {
          width: 36px;
          height: 36px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, rgba(0, 255, 136, 0.3) 0%, rgba(0, 123, 255, 0.3) 100%);
          border-radius: 6px;
          margin-right: 10px;

          i {
            color: #00ff88;
            font-size: 18px;
          }
        }

        .item-content {
          flex: 1;
          .item-title {
            color: #ffffff;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 3px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
          }

          .item-description {
            color: rgba(255, 255, 255, 0.7);
            font-size: 11px;
            line-height: 1.3;
          }
        }

        .item-arrow {
          color: rgba(255, 255, 255, 0.5);
          font-size: 14px;
          transition: all 0.3s ease;
        }

        &:hover .item-arrow {
          color: #00ff88;
          transform: translateX(2px);
        }
      }

      /* 视频搜索和筛选样式 */
      .video-filter {
        margin-bottom: 16px;
        padding: 12px;
        background: rgba(255, 255, 255, 0.03);
        border-radius: 6px;
        border: 1px solid rgba(0, 255, 136, 0.1);
        display: flex;
        flex-direction: column;
        gap: 12px;
        pointer-events: auto;

        /* 搜索框容器 */
        .search-container {
          display: flex;
          gap: 8px;
          align-items: center;

          .search-input {
            flex: 1;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 4px;
            color: #ffffff;
            font-size: 13px;
            outline: none;
            transition: all 0.3s ease;

            &::placeholder {
              color: rgba(255, 255, 255, 0.5);
            }

            &:focus {
              border-color: rgba(0, 255, 136, 0.6);
              background: rgba(255, 255, 255, 0.08);
              box-shadow: 0 0 8px rgba(0, 255, 136, 0.2);
            }
          }

          .search-button {
            padding: 8px 16px;
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.2) 0%, rgba(0, 123, 255, 0.2) 100%);
            border: 1px solid rgba(0, 255, 136, 0.4);
            border-radius: 4px;
            color: #ffffff;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            outline: none;
            transition: all 0.3s ease;
            white-space: nowrap;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);

            &:hover {
              background: linear-gradient(135deg, rgba(0, 255, 136, 0.3) 0%, rgba(0, 123, 255, 0.3) 100%);
              border-color: rgba(0, 255, 136, 0.6);
              box-shadow: 0 0 8px rgba(0, 255, 136, 0.3);
              transform: translateY(-1px);
            }

            &:active {
              transform: translateY(0);
              box-shadow: 0 0 4px rgba(0, 255, 136, 0.4);
            }
          }
        }

        /* 下拉框容器 */
        .select-container {
          display: flex;
          align-items: center;
          gap: 8px;

          .select-label {
            color: #ffffff;
            font-size: 13px;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            white-space: nowrap;
          }

          .category-select {
            flex: 1;
            padding: 6px 10px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 4px;
            color: #ffffff;
            font-size: 12px;
            outline: none;
            cursor: pointer;
            transition: all 0.3s ease;

            &:focus {
              border-color: rgba(0, 255, 136, 0.6);
              background: rgba(255, 255, 255, 0.08);
            }

            option {
              background: #1a1a2e;
              color: #ffffff;
              padding: 4px;
            }
          }
        }
      }

      /* 视频列表样式 */
      .video-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
        pointer-events: auto;
        overflow-y: scroll;

        .video-item {
          display: flex;
          align-items: center;
          padding: 10px 12px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 6px;
          border: 1px solid rgba(0, 255, 136, 0.2);
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(0, 255, 136, 0.1);
            border-color: rgba(0, 255, 136, 0.5);
          }

          .video-status {
            margin-right: 10px;

            .status-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background: #2ed573;
              display: inline-block;
              box-shadow: 0 0 6px rgba(46, 213, 115, 0.6);

              &.offline {
                background: #ff4757;
                box-shadow: 0 0 6px rgba(255, 71, 87, 0.6);
              }
            }
          }

          .video-info {
            flex: 1;

            .video-name {
              color: #ffffff;
              font-size: 13px;
              font-weight: 500;
              margin-bottom: 2px;
              text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
              line-height: 1.3;
            }

            .video-id {
              color: rgba(255, 255, 255, 0.6);
              font-size: 11px;
              font-family: 'Courier New', monospace;
            }
          }
        }
      }
    }

    /* 中间底部背景图样式 */
    .middle-bottom {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 35%;
      height: vh(80);
      background-image: url('@/assets/images/middle-bottom.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      z-index: 90;
    }

    /* 子组件容器样式 */
    .component-container {
      position: relative;
      width: 100%;
      height: 100%;
      pointer-events: auto; // 修改为auto，让子组件可以接收鼠标事件
      z-index: 10;

      /* 为了让子组件中需要点击的元素正常工作，而让其他部分能穿透到地图 */
      > * {
        pointer-events: auto; // 确保子组件内的元素可以接收鼠标事件
      }
    }
  }
}

/* 确保弹窗可以正常交互 */
:deep(.custom-dialog) {
  pointer-events: auto !important;
  z-index: 10000 !important;
}

.map-popup-container {
  pointer-events: auto !important;
  z-index: 10000;

  :deep(.el-dialog__headerbtn .el-dialog__close) {
    color: #ffffff !important;
    font-size: 60px !important;
  }
}

/* 弹窗内容样式 */
.popup-content {
  pointer-events: auto;
  padding: 5px;
  font-size: 24px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 30px;

  .popup-item {
    margin-bottom: 8px;

    color: #ffffff;

    .popup-value {
      color: #ffffff;
      font-weight: bold;
    }
  }
}

/* 鼠标点击指示器样式 */
.click-indicator {
  position: fixed;
  width: 30px;
  height: 30px;
  transform: translate(-50%, -50%);
  pointer-events: none;
  /* 确保指示器不会干扰鼠标事件 */
  z-index: 10000;
}

.click-ripple {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid rgba(255, 0, 0, 0.8);
  animation: ripple 1s ease-out;
}

.click-coords {
  position: absolute;
  top: 30px;
  left: 0;
  white-space: nowrap;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

@keyframes ripple {
  0% {
    transform: scale(0.3);
    opacity: 1;
  }

  100% {
    transform: scale(3);
    opacity: 0;
  }
}
</style>

<style lang="scss" scoped>
:deep(.el-tabs__item) {
  color: #fff !important;
  font-size: 24px !important;
}

:deep(.el-dialog) {
  --el-dialog-bg-color: transparent;
  background-image: url('@/assets/images/tcbg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

:deep(.supplies-dialog .el-dialog .el-dialog__header) {
  background: transparent !important;
}

// :deep(.supplies-dialog .el-dialog) {
//   background: none !important;
// }
:deep(.supplies-dialog .el-dialog) {
  background-image: url('@/assets/images/tcbg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  // background: none !important;
  box-shadow: none !important;
}

:deep(.supplies-dialog .el-dialog .el-dialog__body .el-table__header-wrapper tr) {
  background: #1b72df;
}

.table-preview-overlay {
  border: 1px solid #4190d8;
  position: fixed;
  top: 20%;
  left: 0;
  width: 1000px;
  height: 800px;
  pointer-events: auto;
  background: rgba(26, 72, 123, 0.9693);
  display: flex;
  z-index: 10000;
  backdrop-filter: blur(5px);
  margin-left: 44.5%;
  background: rgba(26, 72, 123, 0.9);
  box-shadow: inset 0px 0px 57px 0px #0e4571;
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #4190d8;

  //opacity: 0.95;
  .car-dialog-header {
    background: linear-gradient(270deg, #2882bf 0%, rgba(38, 128, 190, 0.3862) 100%);
    padding: 10px 20px;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    width: 100%;
    align-items: center;
    font-weight: 600;
    background: linear-gradient(270deg, #2882bf 0%, rgba(38, 128, 190, 0.3862) 100%);
    border-radius: 0px 0px 0px 0px;

    .car-dialog-title {
      color: #ffffff;
      font-size: 38px;
      font-weight: bold;
      margin: 0;
      padding-left: 30px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .car-dialog-close {
      color: #ffffff;
      font-size: 24px;
      cursor: pointer;
      width: 30px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      transition: all 0.3s ease;
    }
  }

  .car-preview-dialog {
    width: 100%;
    margin-top: 90px;

    .car-info {
      margin-top: 50px;
      position: relative;
      height: 50px;
      line-height: 50px;
      font-size: 40px;
      color: #fff;
      width: 90%;
      margin-left: 5%;
      font-size: 32px;
    }

    .car-vd {
      width: 90%;
      margin: 30px auto;
      height: 350px;
      max-height: 350px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;

      .video-player-container {
        width: 100%;
        height: 100%;
        max-height: 350px;
        background: #000;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

        .video-player {
          width: 100%;
          height: 100%;
          max-height: 350px;
          object-fit: contain;
        }
      }

      .no-video-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 350px;
        max-height: 350px;
        background: rgba(255, 255, 255, 0.05);
        border: 2px dashed rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        color: rgba(255, 255, 255, 0.6);

        .no-video-icon {
          font-size: 48px;
          margin-bottom: 16px;
          opacity: 0.5;
        }

        .no-video-text {
          font-size: 18px;
          font-weight: 500;
        }
      }
    }
  }
}

.call-preview-overlay {
  border: 1px solid #4190d8;
  position: fixed;
  top: 20%;
  left: 0;
  width: 1000px;
  height: 800px;
  pointer-events: auto;
  background: rgba(26, 72, 123, 0.9693);
  display: flex;
  z-index: 10000;
  backdrop-filter: blur(5px);
  margin-left: 44.5%;
  background: rgba(26, 72, 123, 0.9);
  box-shadow: inset 0px 0px 57px 0px #0e4571;
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #4190d8;

  //opacity: 0.95;
  .car-dialog-header {
    padding: 10px 20px;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 80px;
    width: 100%;
    align-items: center;
    font-weight: 600;
    // background: linear-gradient(270deg, #2882bf 0%, rgba(38, 128, 190, 0.3862) 100%);
    border-radius: 0px 0px 0px 0px;

    .car-dialog-close {
      color: #ffffff;
      font-size: 50px;
      cursor: pointer;
      width: 30px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      transition: all 0.3s ease;
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 80px);
    margin-top: 80px;
  }
}

.yuntu {
  .el-dialog {
    width: 2200px;
    height: 860px;
    background: transparent;
    box-shadow: none;
  }
}

/* 云图iframe弹窗遮罩层 */
.yuntu-iframe-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  cursor: pointer;
  /* 添加指针样式 */
  pointer-events: auto;
  /* 确保可以接收点击事件 */
}

/* 云图iframe弹窗样式 */
.yuntu-iframe-dialog {
  width: 30%;

  background-color: #fff;
  border-radius: 12px;

  position: relative;
  max-height: 90vh;
  overflow: hidden;

  .dialog-header {
    background-color: #fff;

    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .dialog-title {
      color: #000000;
      font-size: 28px;
      font-weight: 600;
      margin: 0;
    }

    .dialog-close {
      color: #000000;
      font-size: 40px;
      font-weight: bold;
      cursor: pointer;
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      transition: all 0.3s ease;
      pointer-events: auto;

      &:hover {
        color: #333333;
        background-color: rgba(0, 0, 0, 0.1);
      }
    }
  }

  .dialog-content {
    padding: 30px;

    iframe {
      width: 100%;
      height: 1000px;
      border: none;
      border-radius: 8px;
      pointer-events: auto;
      /* 确保iframe可以接收鼠标事件 */
      user-select: auto;
      /* 允许文本选择 */
    }
  }
}

/* 原有云图弹窗样式 */
.yuntu-dialog {
  .el-dialog {
    background: rgba(6, 72, 146, 0.95);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

    .el-dialog__body {
      padding: 20px;
    }

    .el-dialog__close {
      color: #ffffff !important;
      font-size: 20px;

      &:hover {
        color: #409eff !important;
      }
    }
  }
}

/* 确保弹窗关闭按钮可点击 */
.el-dialog__headerbtn,
.el-dialog__close,
.el-icon {
  pointer-events: auto !important;
  z-index: 10001 !important;
}

.supplies-dialog {
  .el-dialog {
    // background: rgba(26, 72, 123, 0.9693);
    // box-shadow: inset 0px 0px 57px 0px #0e4571;

    border-radius: 0px 0px 0px 0px;
    border: 1px solid #4190d8;
    padding: 0;

    .el-dialog__header {
      height: 50px;
      // background: linear-gradient(270deg, #2882bf 0%, rgba(38, 128, 190, 0.3862) 100%);
      border-radius: 0px 0px 0px 0px;
      padding-left: 30px;

      .el-dialog__title {
        line-height: 50px;
        color: #fff;
        font-size: 24px;
      }

      .el-dialog__close {
        color: #fff;
        font-size: 30px;
      }
    }

    .el-dialog__body {
      padding-left: 50px;
      padding-right: 50px;
      padding-bottom: 80px;

      .el-form-item__label {
        color: #fff;
        font-size: 22px;
      }

      .el-select__wrapper {
        background: transparent;

        // box-shadow: 0 0 0 1px #4190d8 inset;
        .el-select__selected-item {
          color: #fff;
          font-size: 18px;
        }
      }

      .el-input__wrapper {
        background: transparent;

        // box-shadow: 0 0 0 1px #4190d8 inset;
        .el-input__inner {
          color: #fff;
          font-size: 18px;
        }

        .el-input__inner::-webkit-input-placeholder {
          color: #fff;
        }

        .el-input__inner::-moz-placeholder {
          color: #fff;
        }
      }

      .el-button {
        background: rgba(0, 147, 255, 0.2);
        border: 1px solid #1f8ad4;
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 500;
        font-size: 18px;
        color: #ffffff;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }

      .el-table__row {
        background: transparent;

        td {
          background: transparent;
          color: #fff;
          font-size: 22px;
          border-bottom: 1px solid rgba(216, 216, 216, 0.2);
          padding: 16px 0;
        }
      }

      .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
        background-color: rgba(31, 138, 212, 0.4);
      }

      .el-table__header-wrapper {
        tr {
          background: transparent;
          // background: rgba(52, 120, 187, 0.3632);
          border-radius: 2px 2px 2px 2px;

          // border: 1px solid rgba(60, 139, 217, 0.4542);
          th {
            background: transparent;
            font-size: 20px;
            color: #fff;
            border-bottom: none;
          }
        }
      }

      .el-table--fit .el-table__inner-wrapper::before {
        width: 0px;
      }

      .el-pager {
        li {
          background: transparent;
          color: #d8d8d8;
          font-size: 18px;

          &.is-active {
            background: #008aff;
          }
        }
      }

      .el-pagination {
        float: right;

        button {
          background: transparent;
        }

        .btn-prev,
        .btn-next {
          color: #fff;
        }

        .el-pagination__total,
        .el-pagination__jump {
          color: #fff;
          font-size: 18px;
        }
      }
    }
  }
}

/* Device Dialog 自定义弹窗样式 */
.device-dialog-overlay {
  position: fixed;
  top: 40px;
  left: 0;
  width: 100%;
  height: 100%;
  // background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  pointer-events: auto;
}

.device-dialog {
  width: 20%;
  height: 71%;
  // background: #0e4571;
  // border: 1px solid #00a2ec;
  // border-radius: 8px;
  // box-shadow: 0 0 20px rgba(0, 162, 236, 0.3);
  background-image: url('@/assets/images/tcbg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .device-dialog-header {
    // background: linear-gradient(270deg, #2882bf 0%, rgba(38, 128, 190, 0.3862) 100%);
    padding: 20px 20px 0 30px;
    display: flex;
    justify-content: space-between;
    // height: 40px;
    width: 100%;
    align-items: center;

    .device-dialog-title {
      color: #ffffff;
      font-size: 24px;
      font-weight: bold;
      margin: 0;
    }

    .device-dialog-close {
      color: #ffffff;
      font-size: 50px;
      cursor: pointer;
      width: 30px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      transition: all 0.3s ease;
    }
  }

  .device-dialog-body {
    padding: 20px;
    // background: #194779;
    flex: 1;
    overflow-y: auto;
  }
}

.device-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  color: #ffffff;
  // background: #194779;
}

.monitoring-section {
  .monitoring-title {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    color: #ffffff;
    //margin-bottom: 15px;
    width: 277px;
    height: 40px;
    background: url('@/assets/images/home/<USER>') no-repeat center/100%;
    padding-left: 30px;

    // .monitoring-icon {
    //   width: 16px;
    //   height: 16px;
    //   background: #00a2ec;
    //   border-radius: 2px;
    //   margin-right: 8px;
    // }
  }

  .charts-container {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    // margin-bottom: 20px;

    .chart-item {
      width: 50%;
      height: 250px;
      // background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
      // border: 1px solid rgba(0, 162, 236, 0.3);
      // border-radius: 4px;
      padding: 10px;

      .chart-wrapper {
        width: 100%;
        height: 100%;
        background: transparent;
      }
    }
  }

  .data-table-section {
    padding-top: 20px;

    .el-tabs__item {
      color: #fff;
      font-size: 26px;
      padding-bottom: 20px;
    }

    .table-container {
      // background: linear-gradient(135deg, #1e4a72 0%, #2a5298 100%);
      padding: 0;
      max-height: 300px; // 设置表格容器的最大高度
      overflow: hidden;
      border: 1px solid rgba(65, 144, 216, 0.3); // 添加边框以明确滚动区域
      border-radius: 4px;

      .info-table {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        background: transparent;
        table-layout: fixed; // 固定表格布局，确保列宽一致

        // 表头样式
        thead {
          display: block;
          width: 100%;
          background: rgba(52, 120, 187, 0.3632);
          border-radius: 2px 2px 2px 2px;
          border: 1px solid rgba(60, 139, 217, 0.4542);

          tr {
            display: table;
            width: 100%;
            table-layout: fixed;

            th {
              display: table-cell;
              color: #ffffff;
              font-size: 24px;
              font-weight: bold;
              text-align: center;
              padding: 8px 15px;
              border-bottom: 1px solid rgba(65, 144, 216, 0.3);

              // 设置每列的固定宽度，确保对齐
              &:nth-child(1) {
                width: 25%;
              }

              // 泵站列
              &:nth-child(2) {
                width: 25%;
              }

              // 权责部门列
              &:nth-child(3) {
                width: 25%;
              }

              // 权责类型列
              &:nth-child(4) {
                width: 25%;
              }

              // 联系方式列
            }
          }
        }

        // 表体样式
        tbody {
          display: block;
          max-height: 172px; // 减去表头高度
          overflow-y: auto; // 垂直滚动
          overflow-x: hidden; // 隐藏水平滚动

          // 自定义滚动条样式
          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: rgba(65, 144, 216, 0.6);
            border-radius: 3px;

            &:hover {
              background: rgba(65, 144, 216, 0.8);
            }
          }

          tr {
            display: table;
            width: 100%;
            table-layout: fixed;
            transition: background-color 0.3s ease;

            td {
              display: table-cell;
              padding: 12px 15px;
              border-bottom: 1px solid rgba(65, 144, 216, 0.3);
              font-size: 24px;
              text-align: center;
              color: #ffffff;
              // 设置文本溢出处理
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;

              // 确保与表头列宽一致
              &:nth-child(1) {
                width: 25%;
              }

              // 泵站列
              &:nth-child(2) {
                width: 25%;
              }

              // 权责部门列
              &:nth-child(3) {
                width: 25%;
              }

              // 权责类型列
              &:nth-child(4) {
                width: 25%;
              }

              // 联系方式列

              &.label-cell {
                color: #ffffff;
                font-weight: 500;
                // background: rgba(40, 130, 191, 0.3);
              }

              &.value-cell {
                color: #ffffff;
              }

              .pump-station-cell {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
                // 确保内容不会溢出
                overflow: hidden;

                .pump-station-thumbnail {
                  width: 32px;
                  height: 32px;
                  border-radius: 4px;
                  object-fit: cover;
                  flex-shrink: 0; // 防止图片被压缩
                }

                span {
                  color: #ffffff;
                  font-size: 24px;
                  // 文本溢出处理
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  flex: 1;
                }
              }
            }
          }
        }
      }
    }
  }
}

.video-section {
  .video-title {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 15px;
    width: 277px;
    height: 29px;
    background: url('@/assets/images/home/<USER>') no-repeat center/100%;
    padding-left: 30px;

    .video-icon {
      width: 16px;
      height: 16px;
      background: #00a2ec;
      border-radius: 2px;
      margin-right: 8px;
    }
  }

  .video-content {
    width: 100%;
    padding: 15px;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 20px;

    .video-info-list {
      display: flex;
      flex-direction: column;
      gap: 10px;
      min-width: 200px;

      .video-info {
        color: #ffffff;
        font-size: 24px;
        padding: 10px 15px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid transparent;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(0, 162, 236, 0.5);
        }

        &.active {
          background: rgba(0, 162, 236, 0.3);
          border-color: #00a2ec;
          color: #00a2ec;
        }
      }
    }

    .video-player {
      position: relative;
      width: 397px;
      height: 199px;
      background: #000000;
      border-radius: 4px;
      overflow: hidden;
    }

    .video-player-placeholder {
      position: relative;
      width: 397px;
      height: 199px;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 2px dashed rgba(255, 255, 255, 0.3);

      .placeholder-text {
        color: rgba(255, 255, 255, 0.6);
        font-size: 24px;
      }
    }
  }
}

// 图片预览弹窗样式
.image-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: auto;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  backdrop-filter: blur(5px);

  .image-preview-dialog {
    position: relative;
    max-width: 100%;
    max-height: 100%;
    background: rgba(30, 74, 114, 0.95);
    border-radius: 12px;
    border: 2px solid rgba(65, 144, 216, 0.6);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    animation: imagePreviewFadeIn 0.3s ease-out;

    .image-preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      background: rgba(52, 120, 187, 0.8);
      border-bottom: 1px solid rgba(65, 144, 216, 0.3);

      .image-preview-title {
        color: #ffffff;
        font-size: 24px;
        font-weight: bold;
        font-family: 'Microsoft YaHei', sans-serif;
      }

      .image-preview-close {
        color: #ffffff;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        width: 32px;
        height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: scale(1.1);
        }
      }
    }

    .image-preview-content {
      padding: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;

      .preview-image {
        max-width: 100%;
        max-height: 70vh;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        transition: transform 0.3s ease;
      }
    }
  }
}

// 人员定位统计样式
.personnel-stats-container {
  padding: 20px;

  .personnel-stats-card {
    background: linear-gradient(135deg, rgba(30, 74, 114, 0.9), rgba(52, 120, 187, 0.8));
    border-radius: 12px;
    border: 2px solid rgba(65, 144, 216, 0.6);
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);

    .stats-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      margin-bottom: 12px;
      border-radius: 8px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .stats-label {
        font-size: 18px;
        font-weight: 500;
        color: #ffffff;
        font-family: 'Microsoft YaHei', sans-serif;
      }

      .stats-value {
        font-size: 24px;
        font-weight: bold;
        font-family: 'Arial', sans-serif;
      }

      &.all {
        background: linear-gradient(90deg, rgba(65, 144, 216, 0.3), rgba(65, 144, 216, 0.1));

        .stats-value {
          color: #4190d8;
        }
      }

      &.online {
        background: linear-gradient(90deg, rgba(0, 255, 136, 0.3), rgba(0, 255, 136, 0.1));

        .stats-value {
          color: #00ff88;
        }
      }

      &.offline {
        background: linear-gradient(90deg, rgba(255, 107, 107, 0.3), rgba(255, 107, 107, 0.1));

        .stats-value {
          color: #ff6b6b;
        }
      }
    }
  }
}

// 重点场景选择样式
.scene-item {
  .item-content {
    display: flex;
    align-items: center;
    gap: 12px;
    justify-content: space-between;
    width: 100%;
  }

  .item-checkbox {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(65, 144, 216, 0.6);
    border-radius: 4px;
    background: rgba(30, 74, 114, 0.3);
    transition: all 0.3s ease;

    .checkbox-icon {
      color: #00ff88;
      font-size: 14px;
      font-weight: bold;
      line-height: 1;

      &.checked {
        animation: checkboxPulse 0.3s ease;
      }
    }
  }

  &.selected {
    .item-checkbox {
      background: rgba(0, 255, 136, 0.2);
      border-color: #00ff88;
      box-shadow: 0 0 8px rgba(0, 255, 136, 0.3);
    }

    .item-title {
      color: #00ff88;
    }
  }

  &:hover {
    .item-checkbox {
      border-color: #4190d8;
      box-shadow: 0 0 6px rgba(65, 144, 216, 0.4);
    }
  }

  .item-title {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    flex: 1;
  }

  .item-count {
    color: #00ff88;
    font-size: 12px;
    font-weight: 400;
    margin-left: auto;
    opacity: 0.8;
  }
}

@keyframes checkboxPulse {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// 缩略图点击效果
.pump-station-thumbnail {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid rgba(65, 144, 216, 0.5);
}

// 弹窗动画
@keyframes imagePreviewFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>

<style lang="scss" scoped>
.el-slider__bar,
.el-slider__button,
.el-slider,
.el-slider__runway,
.el-slider__button-wrapper,
.el-button {
  pointer-events: auto;
  /* 确保这不是设置在滑块或其父元素上的 */
}

.progress-wrapper {
  width: 2100px;
}

.el-slider {
  width: 2100px;
  height: 150px;
  padding-left: 80px;
  padding-right: 80px;
}

.el-slider__runway,
.el-slider__bar,
.el-slider__button-wrapper,
el-slider__button {
  height: 40px;
}

.el-slider__stop,
.el-slider__button {
  width: 40px;
  height: 40px;
}

.el-slider__button-wrapper {
  top: 0;
}

.el-slider__marks-text {
  font-size: 32px;
  bottom: -42px;
}

.yuntu {
  .el-dialog__close {
    font-size: 48px;
    color: #fff;
  }
}

:deep(.flood-report-dialog .el-table){
  --el-table-bg-color:transparent !important;
}
/* 防汛简报弹窗样式 */
:deep(.flood-report-dialog .el-dialog) {
  background: transparent !important;
}

:deep(.flood-report-dialog .el-dialog__header) {
  background: transparent !important;
  padding: 16px 30px 16px 20px;
 
}

:deep(.flood-report-dialog .el-dialog__title) {
  font-size: 26px;
  font-weight: bold;
   color: #fff !important;
}

:deep(.flood-report-dialog .el-dialog__close) {
  color: #ffffff;
  font-size: 30px;
  margin-right: 20px;
}

:deep(.flood-report-dialog .el-dialog__close:hover) {
  color: #60a5fa;
}

:deep(.flood-report-dialog .el-dialog__body) {
  padding: 0;
   background: transparent !important;
}

.flood-report-container {
  width: 100%;
   background: transparent !important;
  padding: 20px;
}

/* 表格样式 */
.flood-table-wrapper {
  // background: #1e3a8a;
  // border: 1px solid #2563eb;
  border-radius: 4px;
}

.flood-native-table {
  width: 100%;
  border-collapse: collapse;
  // background: #1e3a8a;
}

.flood-native-table thead th {
  color: #ffffff !important;
  font-size: 26px !important;
  text-align: left;
  padding: 12px 8px !important;
  position: sticky;
  top: 0;
  z-index: 10;
}



.flood-native-table tbody td {
  color: #ffffff !important;
  font-size: 26px !important;
  text-align: left;
  padding: 12px 8px !important;
}


/* 操作按钮样式 */
.flood-action-btn {
  padding: 4px 12px !important;
  font-size: 12px !important;
  border-radius: 4px !important;
  border: none !important;
}

.preview-btn {
  background: #3b82f6 !important;
  color: #ffffff !important;
}

.preview-btn:hover {
  background: #2563eb !important;
}

.download-btn {
  background: #10b981 !important;
  color: #ffffff !important;
}

.download-btn:hover {
  background: #059669 !important;
}

/* 分页样式 */
.flood-pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 20px;
  padding: 0 20px;
}

.pagination-info {
  color: #ffffff;
  font-size: 14px;
}

.pagination-controls {
  display: flex;
  align-items: center;
}

:deep(.flood-report-pagination) {
  background: transparent;
}

:deep(.flood-report-pagination .el-pagination__total) {
  color: #ffffff;
  margin-right: 10px;
}

:deep(.flood-report-pagination .el-pagination__sizes) {
  margin-right: 10px;
}

:deep(.flood-report-pagination .el-pager) {
  background: transparent;
}

:deep(.flood-report-pagination .el-pager li) {
  background: #2563eb;
  border: 1px solid #3b82f6;
  color: #ffffff;
  // margin: 0 2px;
  // border-radius: 4px;
  // min-width: 32px;
  // height: 32px;
  // line-height: 30px;
}

:deep(.flood-report-pagination .el-pager li:hover) {
  background: #3b82f6;
  color: #ffffff;
}

:deep(.flood-report-pagination .el-pager li.active) {
  background: #1d4ed8;
  color: #ffffff;
  border-color: #1d4ed8;
}

:deep(.flood-report-pagination .btn-prev),
:deep(.flood-report-pagination .btn-next) {
  background: #2563eb;
  border: 1px solid #3b82f6;
  color: #ffffff;
  margin: 0 5px;
  // margin: 0 5px;
  border-radius: 4px;
  // min-width: 32px;
  // height: 32px;
}

:deep(.flood-report-pagination .btn-prev:hover),
:deep(.flood-report-pagination .btn-next:hover) {
  background: #3b82f6;
  color: #ffffff;
}

:deep(.flood-report-pagination .el-pagination__jump) {
  color: #ffffff;
  margin-left: 10px;
}


</style>
